2025-08-19T08:20:44.427+08:00  INFO 21596 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 21596 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T08:20:44.450+08:00 DEBUG 21596 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T08:20:44.450+08:00  INFO 21596 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T08:20:50.591+08:00  INFO 21596 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T08:20:51.110+08:00  INFO 21596 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 322 ms. Found 42 JPA repository interfaces.
2025-08-19T08:20:52.512+08:00  INFO 21596 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T08:20:52.526+08:00  INFO 21596 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T08:20:52.526+08:00  INFO 21596 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T08:20:52.638+08:00  INFO 21596 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T08:20:52.639+08:00  INFO 21596 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 8089 ms
2025-08-19T08:20:52.789+08:00 DEBUG 21596 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T08:20:53.223+08:00  INFO 21596 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T08:20:53.392+08:00  INFO 21596 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T08:20:53.485+08:00  INFO 21596 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T08:20:53.687+08:00  INFO 21596 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T08:20:54.736+08:00  INFO 21596 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@59b98ad1
2025-08-19T08:20:54.736+08:00  INFO 21596 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T08:20:55.356+08:00  INFO 21596 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T08:20:57.305+08:00  INFO 21596 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T08:20:57.840+08:00  INFO 21596 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T08:20:58.353+08:00  INFO 21596 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T08:21:00.860+08:00  INFO 21596 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T08:21:01.040+08:00  INFO 21596 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T08:21:01.089+08:00  INFO 21596 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@279a3298, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@42e1aecb, org.springframework.security.web.context.SecurityContextHolderFilter@5fd01e54, org.springframework.security.web.header.HeaderWriterFilter@69fcced9, org.springframework.web.filter.CorsFilter@fa230d1, org.springframework.security.web.authentication.logout.LogoutFilter@4f80d322, com.heating.filter.JwtAuthenticationFilter@20256a0b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7de72197, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4145bb9f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@608019af, org.springframework.security.web.access.ExceptionTranslationFilter@67481b2b, org.springframework.security.web.access.intercept.AuthorizationFilter@6aa658c3]
2025-08-19T08:21:01.573+08:00  INFO 21596 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T08:21:01.581+08:00  INFO 21596 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 18.308 seconds (process running for 20.424)
2025-08-19T08:23:12.798+08:00  INFO 21596 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-19T08:23:12.799+08:00  INFO 21596 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-19T08:23:12.801+08:00  INFO 21596 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-19T08:23:12.826+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:23:12.860+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:23:12.972+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:12.973+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:13.372+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:23:13.372+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:23:13.520+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:23:13.521+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:23:13.909+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:23:13.909+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:23:13.909+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:23:13.910+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:23:13.945+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:23:13.945+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:23:13.945+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:23:14.139+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:23:14.185+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:23:14.226+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:23:20.976+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.977+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.978+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.978+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:20.978+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:20.978+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.979+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:20.979+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:21.204+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:23:21.204+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:23:21.214+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:23:21.214+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:23:21.239+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:23:21.239+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:23:21.249+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:23:21.250+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:23:21.661+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:23:21.661+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:23:21.661+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:23:21.690+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:23:21.690+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:23:21.690+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:23:21.855+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:23:21.855+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:23:21.890+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:23:21.930+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:32:27.762+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.763+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.764+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.765+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.765+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.765+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.766+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.766+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.992+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:32:27.992+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:32:28.001+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:32:28.002+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:32:28.026+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:32:28.026+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:32:28.037+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:32:28.037+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:32:28.446+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:32:28.446+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:32:28.446+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:32:28.476+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:32:28.476+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:32:28.476+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:32:28.674+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:32:28.674+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:32:28.714+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:44:55.926+08:00 DEBUG 21596 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:44:55.927+08:00 DEBUG 21596 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:44:55.929+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:44:55.929+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:44:56.158+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:44:56.158+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:44:56.192+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:44:56.192+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:44:56.579+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:44:56.579+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:44:56.579+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:44:56.579+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:44:56.613+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:44:56.613+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:44:56.613+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:44:56.811+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:44:56.848+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:44:56.848+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:44:56.848+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:44:56.848+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:44:56.848+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:44:56.850+08:00 DEBUG 21596 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:49:20.103+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T08:49:20.107+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T08:49:20.135+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T08:49:20.135+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T08:49:20.137+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T08:49:20.138+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T08:49:20.364+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T08:49:20.373+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T08:49:20.511+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T08:49:20.514+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:49:20.642+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T08:49:20.643+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:49:23.082+08:00 DEBUG 21596 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail-by-id?faultId=33
2025-08-19T08:49:23.083+08:00 DEBUG 21596 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail-by-id?faultId=33
2025-08-19T08:49:23.092+08:00 DEBUG 21596 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:49:26.922+08:00 DEBUG 21596 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T08:49:26.922+08:00 DEBUG 21596 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T08:49:26.925+08:00 DEBUG 21596 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:49:36.237+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T08:49:36.237+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T08:49:36.240+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T08:49:36.240+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T08:49:36.240+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T08:49:36.240+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T08:49:36.241+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T08:49:36.241+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T08:49:36.501+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T08:49:36.505+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:49:36.511+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T08:49:36.512+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:51:40.688+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:51:40.688+08:00 DEBUG 21596 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:51:40.688+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:51:40.688+08:00 DEBUG 21596 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:51:40.688+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:40.688+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:40.689+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:40.689+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:40.913+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:51:40.913+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:51:40.918+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:51:40.918+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:51:40.947+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:51:40.947+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:51:40.952+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:51:40.952+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:51:41.331+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:51:41.331+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:51:41.331+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:51:41.331+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:51:41.347+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:51:41.347+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:51:41.347+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:51:41.347+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:51:41.364+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:51:41.364+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:51:41.364+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:51:41.381+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:51:41.381+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:51:41.381+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:51:41.556+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:51:41.557+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:51:41.579+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:51:41.580+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:51:41.591+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:51:41.591+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:51:41.591+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:51:41.591+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:51:41.591+08:00  INFO 21596 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:51:41.592+08:00 DEBUG 21596 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:51:41.614+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:51:41.615+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:51:41.615+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:51:41.615+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:51:41.615+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:51:41.616+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:51:46.872+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:51:46.872+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:51:46.873+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:46.873+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:47.097+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:51:47.097+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:51:47.131+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:51:47.131+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:51:47.516+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:51:47.516+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:51:47.516+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:51:47.516+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:51:47.549+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:51:47.549+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:51:47.549+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:51:47.742+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:51:47.776+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:51:47.776+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:51:47.776+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:51:47.776+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:51:47.776+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:51:47.777+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:51:52.057+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:51:52.057+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:51:52.058+08:00 DEBUG 21596 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:51:52.058+08:00 DEBUG 21596 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:51:52.059+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:52.059+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:52.059+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:52.059+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:51:52.284+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:51:52.284+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:51:52.290+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:51:52.290+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:51:52.317+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:51:52.317+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:51:52.324+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:51:52.324+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:51:52.702+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:51:52.702+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:51:52.702+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:51:52.702+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:51:52.720+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:51:52.721+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:51:52.721+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:51:52.721+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:51:52.735+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:51:52.735+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:51:52.735+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:51:52.755+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:51:52.755+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:51:52.755+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:51:52.926+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:51:52.927+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:51:52.953+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:51:52.960+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:51:52.960+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:51:52.960+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:51:52.960+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:51:52.960+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:51:52.961+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:51:52.987+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:51:52.987+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:51:52.988+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:51:52.988+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:51:52.988+08:00  INFO 21596 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:51:52.988+08:00 DEBUG 21596 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:00:09.043+08:00  WARN 21596 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@38bfe97b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-08-19T09:00:24.909+08:00  WARN 21596 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1ef0ef7e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-08-19T09:00:30.444+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T09:00:30.444+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T09:00:30.444+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T09:00:30.445+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T09:00:30.446+08:00 DEBUG 21596 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T09:00:30.446+08:00 DEBUG 21596 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T09:00:30.447+08:00  INFO 21596 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T09:00:30.447+08:00  INFO 21596 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T09:00:34.926+08:00  WARN 21596 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@91ebb8e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-08-19T09:00:40.471+08:00  WARN 21596 --- [http-nio-8889-exec-9] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2da9f24a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-08-19T09:00:40.471+08:00  WARN 21596 --- [http-nio-8889-exec-3] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3751ba6f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-08-19T09:00:40.727+08:00  INFO 21596 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T09:00:40.728+08:00 DEBUG 21596 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:00:40.731+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T09:00:40.731+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:13:37.085+08:00  INFO 21596 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T09:13:37.087+08:00  INFO 21596 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-19T09:13:37.261+08:00  INFO 21596 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-19T09:13:43.649+08:00  INFO 8060 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 8060 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T09:13:43.650+08:00 DEBUG 8060 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T09:13:43.651+08:00  INFO 8060 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T09:13:44.306+08:00  INFO 8060 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T09:13:44.443+08:00  INFO 8060 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 131 ms. Found 42 JPA repository interfaces.
2025-08-19T09:13:44.996+08:00  INFO 8060 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T09:13:45.004+08:00  INFO 8060 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T09:13:45.005+08:00  INFO 8060 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T09:13:45.045+08:00  INFO 8060 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T09:13:45.045+08:00  INFO 8060 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1363 ms
2025-08-19T09:13:45.106+08:00 DEBUG 8060 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T09:13:45.216+08:00  INFO 8060 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T09:13:45.267+08:00  INFO 8060 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T09:13:45.296+08:00  INFO 8060 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T09:13:45.386+08:00  INFO 8060 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T09:13:46.057+08:00  INFO 8060 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e1a3d5a
2025-08-19T09:13:46.059+08:00  INFO 8060 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T09:13:46.333+08:00  INFO 8060 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T09:13:47.610+08:00  INFO 8060 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T09:13:48.331+08:00  INFO 8060 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T09:13:48.567+08:00  INFO 8060 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T09:13:50.594+08:00  INFO 8060 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T09:13:50.700+08:00  INFO 8060 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T09:13:50.735+08:00  INFO 8060 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@40b0b906, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3ae8f170, org.springframework.security.web.context.SecurityContextHolderFilter@59a09a27, org.springframework.security.web.header.HeaderWriterFilter@fa230d1, org.springframework.web.filter.CorsFilter@59c81e46, org.springframework.security.web.authentication.logout.LogoutFilter@12f4f654, com.heating.filter.JwtAuthenticationFilter@2ce524d2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@27b72947, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@76d04a25, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19a51054, org.springframework.security.web.access.ExceptionTranslationFilter@279a3298, org.springframework.security.web.access.intercept.AuthorizationFilter@59168cdc]
2025-08-19T09:13:51.015+08:00  INFO 8060 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T09:13:51.024+08:00  INFO 8060 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.73 seconds (process running for 8.349)
2025-08-19T09:15:09.730+08:00  INFO 8060 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-19T09:15:09.730+08:00  INFO 8060 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-19T09:15:09.731+08:00  INFO 8060 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-19T09:15:09.744+08:00 DEBUG 8060 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T09:15:09.751+08:00 DEBUG 8060 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T09:15:09.810+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:15:09.811+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:15:10.111+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T09:15:10.111+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T09:15:10.208+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:15:10.208+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:15:10.628+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:15:10.629+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:15:10.629+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T09:15:10.629+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T09:15:10.668+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:15:10.668+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T09:15:10.668+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T09:15:10.879+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:15:10.927+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:15:10.928+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:15:10.928+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:15:10.928+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T09:15:10.928+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T09:15:10.959+08:00 DEBUG 8060 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:16:21.647+08:00 DEBUG 8060 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:16:21.647+08:00 DEBUG 8060 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:16:21.649+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:16:21.649+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:16:21.932+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:16:21.932+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:16:21.932+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:16:21.970+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:16:21.970+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:16:22.390+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:16:22.427+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:16:22.428+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:16:22.431+08:00 DEBUG 8060 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:16:24.794+08:00 DEBUG 8060 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:16:24.794+08:00 DEBUG 8060 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:16:24.795+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:16:24.795+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:16:25.076+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:16:25.076+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:16:25.076+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:16:25.114+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:16:25.114+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:16:25.533+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:16:25.569+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:16:25.569+08:00  INFO 8060 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:16:25.570+08:00 DEBUG 8060 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:16:44.196+08:00 DEBUG 8060 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:16:44.197+08:00 DEBUG 8060 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:16:46.266+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:16:47.378+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:16:47.680+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:16:47.680+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:16:47.681+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:16:47.729+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:16:47.730+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:16:48.167+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:16:48.210+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:16:48.842+08:00  INFO 8060 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:17:12.982+08:00 DEBUG 8060 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:18:02.869+08:00 DEBUG 8060 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:18:02.869+08:00 DEBUG 8060 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:18:04.539+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:18:04.539+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:18:04.822+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:18:04.822+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:18:04.822+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:18:04.859+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:18:04.860+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:18:05.279+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:18:05.315+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:18:05.315+08:00  INFO 8060 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:18:05.316+08:00 DEBUG 8060 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:19:04.806+08:00 DEBUG 8060 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:19:04.806+08:00 DEBUG 8060 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:19:04.807+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:19:04.807+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:19:05.089+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:19:05.089+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:19:05.089+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:19:05.126+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:19:05.126+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:19:05.545+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:19:05.581+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:19:05.581+08:00  INFO 8060 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:19:36.229+08:00 DEBUG 8060 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:19:36.261+08:00  WARN 8060 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Thread starvation or clock leap detected (housekeeper delta=49s960ms708µs200ns).
2025-08-19T09:21:07.252+08:00 DEBUG 8060 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:21:07.252+08:00 DEBUG 8060 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:21:07.253+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:21:07.253+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:21:07.536+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:21:07.536+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:21:07.536+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:21:07.572+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:21:07.572+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:21:07.992+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:21:08.027+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:21:08.028+08:00  INFO 8060 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:21:08.028+08:00 DEBUG 8060 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:22:06.411+08:00 DEBUG 8060 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:22:06.412+08:00 DEBUG 8060 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:22:06.413+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:22:06.413+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:22:06.695+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:22:06.695+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:22:06.695+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:22:06.732+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:22:06.732+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:22:07.151+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:22:07.188+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:22:07.188+08:00  INFO 8060 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:22:07.189+08:00 DEBUG 8060 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:24:48.606+08:00 DEBUG 8060 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T09:24:48.606+08:00 DEBUG 8060 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T09:24:48.607+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:24:48.607+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:24:48.607+08:00 DEBUG 8060 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T09:24:48.607+08:00 DEBUG 8060 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T09:24:48.608+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:24:48.608+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:24:48.851+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T09:24:48.851+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T09:24:48.852+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T09:24:48.853+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T09:24:48.887+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:24:48.888+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:24:48.889+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:24:48.889+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:24:49.306+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:24:49.306+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:24:49.306+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T09:24:49.306+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T09:24:49.307+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:24:49.307+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:24:49.307+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T09:24:49.307+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T09:24:49.342+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:24:49.342+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T09:24:49.342+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T09:24:49.343+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:24:49.343+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T09:24:49.343+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T09:24:49.552+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T09:24:49.553+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T09:24:49.555+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:24:49.589+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:24:49.589+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:24:49.590+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:24:49.590+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T09:24:49.590+08:00  INFO 8060 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T09:24:49.590+08:00 DEBUG 8060 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:24:49.591+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:24:49.591+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:24:49.591+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:24:49.591+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T09:24:49.591+08:00  INFO 8060 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T09:24:49.592+08:00 DEBUG 8060 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:24:53.962+08:00 DEBUG 8060 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:24:53.963+08:00 DEBUG 8060 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:24:53.964+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:24:53.965+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息: houseId=7, heatingYear=2025
2025-08-19T09:24:54.246+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:24:54.246+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态: partial_paid
2025-08-19T09:24:54.246+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:24:54.283+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:24:54.283+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:24:54.703+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:24:54.739+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:24:54.739+08:00  INFO 8060 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:24:54.740+08:00 DEBUG 8060 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:29:01.053+08:00  INFO 8060 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T09:29:01.054+08:00  INFO 8060 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-19T09:29:01.247+08:00  INFO 8060 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-19T09:29:11.655+08:00  INFO 1980 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 1980 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T09:29:11.657+08:00 DEBUG 1980 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T09:29:11.657+08:00  INFO 1980 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T09:29:12.316+08:00  INFO 1980 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T09:29:12.437+08:00  INFO 1980 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 116 ms. Found 42 JPA repository interfaces.
2025-08-19T09:29:13.156+08:00  INFO 1980 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T09:29:13.166+08:00  INFO 1980 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T09:29:13.166+08:00  INFO 1980 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T09:29:13.210+08:00  INFO 1980 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T09:29:13.210+08:00  INFO 1980 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1523 ms
2025-08-19T09:29:13.283+08:00 DEBUG 1980 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T09:29:13.394+08:00  INFO 1980 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T09:29:13.451+08:00  INFO 1980 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T09:29:13.479+08:00  INFO 1980 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T09:29:13.567+08:00  INFO 1980 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T09:29:14.176+08:00  INFO 1980 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@7d17906
2025-08-19T09:29:14.177+08:00  INFO 1980 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T09:29:14.461+08:00  INFO 1980 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T09:29:15.735+08:00  INFO 1980 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T09:29:16.318+08:00  INFO 1980 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T09:29:16.501+08:00  INFO 1980 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T09:29:18.463+08:00  INFO 1980 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T09:29:18.563+08:00  INFO 1980 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T09:29:18.603+08:00  INFO 1980 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6e9f587, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6cfbdf32, org.springframework.security.web.context.SecurityContextHolderFilter@73350232, org.springframework.security.web.header.HeaderWriterFilter@2514fb25, org.springframework.web.filter.CorsFilter@72ec0943, org.springframework.security.web.authentication.logout.LogoutFilter@2106fb7d, com.heating.filter.JwtAuthenticationFilter@578c3fd9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54fdefc5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@68fbbb93, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27b611aa, org.springframework.security.web.access.ExceptionTranslationFilter@54fcbf16, org.springframework.security.web.access.intercept.AuthorizationFilter@66505b79]
2025-08-19T09:29:18.917+08:00  INFO 1980 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T09:29:18.928+08:00  INFO 1980 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.624 seconds (process running for 8.293)
2025-08-19T09:29:19.893+08:00  INFO 1980 --- [http-nio-8889-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-19T09:29:19.894+08:00  INFO 1980 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-19T09:29:19.895+08:00  INFO 1980 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-19T09:29:19.907+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:29:19.913+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:29:19.975+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:29:19.975+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T09:29:20.352+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:29:20.353+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T09:29:20.353+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:29:20.400+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:29:20.401+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:29:20.401+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:29:20.807+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:29:20.807+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:29:20.843+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:29:20.844+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:29:20.879+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:29:20.881+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:29:20.881+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:29:20.881+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:29:31.608+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:29:37.229+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:29:37.229+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:29:37.230+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:29:37.231+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T09:29:37.509+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:29:37.509+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T09:29:37.509+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:29:37.543+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:29:37.543+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:29:37.543+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:29:38.201+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:29:38.201+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:29:38.235+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:29:38.235+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:29:38.269+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:29:38.269+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:29:38.269+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:29:38.269+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:29:38.270+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:35:48.922+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:35:48.924+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:35:48.926+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:35:48.926+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T09:35:49.188+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:35:49.188+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T09:35:49.188+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:35:49.223+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:35:49.223+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:35:49.223+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:35:49.623+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:35:49.623+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:35:49.657+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:35:49.658+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:35:49.692+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:35:49.692+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:35:49.692+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:35:49.692+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:35:49.693+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:36:32.149+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:36:32.150+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:36:32.150+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:36:32.150+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T09:36:32.415+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:36:32.415+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T09:36:32.415+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:36:32.450+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:36:32.450+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:36:32.450+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:36:32.842+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:36:32.842+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:36:32.876+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:36:32.877+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:36:32.911+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:36:32.912+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:36:32.912+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:36:32.912+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:36:32.914+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:38:00.100+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:38:00.101+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:38:00.102+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:38:00.102+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T09:38:00.373+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:38:00.373+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T09:38:00.373+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:38:00.408+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:38:00.408+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:38:00.408+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:38:00.824+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:38:00.824+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:38:00.858+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:38:00.858+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:38:00.891+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:38:00.891+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:38:00.891+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:38:00.891+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:38:00.892+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:59:23.427+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T09:59:23.428+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T09:59:23.429+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T09:59:23.429+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T09:59:23.722+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T09:59:23.722+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T09:59:23.722+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T09:59:23.761+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T09:59:23.761+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:59:23.761+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:59:24.197+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:59:24.197+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:59:24.235+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:59:24.235+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:59:24.279+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:59:24.279+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:59:24.279+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:59:24.279+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T09:59:24.280+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:59:28.774+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T09:59:28.774+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T09:59:28.774+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T09:59:28.774+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T09:59:28.775+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:59:28.775+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:59:28.775+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:59:28.775+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T09:59:29.008+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T09:59:29.008+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T09:59:29.030+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T09:59:29.030+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T09:59:29.041+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:59:29.041+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:59:29.074+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T09:59:29.074+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T09:59:29.447+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:59:29.447+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:59:29.447+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T09:59:29.447+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T09:59:29.480+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:59:29.481+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T09:59:29.481+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T09:59:29.515+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T09:59:29.515+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T09:59:29.515+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T09:59:29.515+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T09:59:29.551+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T09:59:29.551+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T09:59:29.552+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T09:59:29.680+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:59:29.725+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:59:29.725+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:59:29.725+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:59:29.725+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T09:59:29.725+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T09:59:29.726+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T09:59:29.785+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T09:59:29.824+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T09:59:29.825+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T09:59:29.825+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T09:59:29.825+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T09:59:29.825+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T09:59:29.825+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:00:23.185+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:00:23.185+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:00:23.186+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:00:23.186+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:00:23.479+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T10:00:23.480+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T10:00:23.480+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T10:00:23.518+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T10:00:23.518+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:00:23.518+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:00:23.972+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:00:23.972+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T10:00:24.016+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:00:24.016+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:00:24.054+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T10:00:24.054+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:00:24.054+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T10:00:24.055+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T10:00:24.055+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:12.194+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:01:12.201+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:01:12.203+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:01:12.206+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:01:12.206+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:01:12.208+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:01:12.440+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:01:12.472+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:01:12.560+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:01:12.563+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:12.712+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:01:12.713+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:13.651+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:01:13.651+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:01:13.660+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:27.169+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:27.169+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:27.191+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T10:01:27.445+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T10:01:27.520+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:01:27.521+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:27.971+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%B7%B2%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:27.972+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%B7%B2%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:27.972+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: 已确认
2025-08-19T10:01:28.190+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: 已确认
2025-08-19T10:01:28.273+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T10:01:28.274+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:28.577+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%B7%B2%E9%80%80%E5%9B%9E
2025-08-19T10:01:28.577+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%B7%B2%E9%80%80%E5%9B%9E
2025-08-19T10:01:28.577+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: 已退回
2025-08-19T10:01:28.794+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: 已退回
2025-08-19T10:01:28.869+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T10:01:28.869+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:29.172+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%B7%B2%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:29.172+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%B7%B2%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:29.172+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: 已确认
2025-08-19T10:01:29.410+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: 已确认
2025-08-19T10:01:29.468+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:29.468+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T10:01:29.469+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T10:01:29.484+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T10:01:29.485+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:29.740+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T10:01:29.800+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:01:29.801+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:01:29.801+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:01:29.814+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:01:29.815+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:29.995+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:01:30.064+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:01:30.065+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:30.669+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:01:30.669+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:01:30.688+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:40.303+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T10:01:40.304+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T10:01:40.306+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T10:01:40.306+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T10:01:40.316+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T10:01:40.316+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T10:01:40.317+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T10:01:40.317+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T10:01:40.570+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T10:01:40.573+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:40.619+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T10:01:40.620+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:42.548+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/invoice-detail
2025-08-19T10:01:42.549+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/invoice-detail
2025-08-19T10:01:42.563+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 获取票据详情: paymentId=12
2025-08-19T10:01:42.563+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 获取票据详情: paymentId=12
2025-08-19T10:01:43.290+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:01:43.291+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:45.770+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T10:01:45.770+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T10:01:45.771+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T10:01:45.771+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T10:01:46.073+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T10:01:46.074+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:50.713+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:01:50.714+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:01:50.714+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:01:50.714+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:01:50.714+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:01:50.715+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:01:50.965+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:01:50.970+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:01:51.033+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:01:51.034+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:01:51.192+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:01:51.193+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:02:15.947+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/data/5
2025-08-19T10:02:15.947+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/data/5
2025-08-19T10:02:16.023+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:02:33.632+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:02:33.633+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:02:33.633+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:02:33.637+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:02:33.638+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:02:33.638+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:02:33.871+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:02:33.900+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:02:33.937+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:02:33.938+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:02:34.135+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:02:34.136+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:03:48.790+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:03:48.790+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:03:48.790+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:03:48.792+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:03:48.793+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:03:48.793+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:03:49.019+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:03:49.069+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:03:49.087+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:03:49.088+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:03:49.293+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:03:49.294+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:03:51.426+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:03:51.427+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:03:51.429+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:03:54.437+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:03:54.437+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:03:54.438+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:03:54.438+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:03:54.438+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:03:54.439+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:03:54.496+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:03:54.497+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail-by-id?faultId=34
2025-08-19T10:03:54.498+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:03:54.665+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:03:54.699+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:03:54.773+08:00  INFO 1980 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:03:54.774+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:03:54.875+08:00  INFO 1980 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:03:54.876+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:05:03.099+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:05:03.099+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:05:03.099+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:05:03.106+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:05:03.106+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:05:03.106+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:05:03.117+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:05:03.118+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:05:03.120+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:05:03.325+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:05:03.360+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:05:03.434+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:05:03.435+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:05:03.530+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:05:03.531+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:05:08.402+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:05:08.402+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:05:08.402+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:05:08.407+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:05:08.408+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:05:08.408+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:05:08.646+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:05:08.657+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:05:08.720+08:00  INFO 1980 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:05:08.721+08:00 DEBUG 1980 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:05:08.892+08:00  INFO 1980 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:05:08.893+08:00 DEBUG 1980 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:05:09.824+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:05:09.825+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:05:09.826+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:05:32.546+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:05:32.546+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:05:32.547+08:00 DEBUG 1980 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:11:31.096+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:11:31.096+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:11:31.097+08:00 DEBUG 1980 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:11:46.565+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:11:46.565+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:11:46.565+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:11:46.567+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:11:46.568+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:11:46.568+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:11:46.590+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:11:46.590+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:11:46.592+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 34, 用户ID: 4
2025-08-19T10:11:46.815+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:11:46.833+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:11:46.833+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 34
2025-08-19T10:11:46.882+08:00  INFO 1980 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:11:46.883+08:00 DEBUG 1980 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:11:46.908+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 34
2025-08-19T10:11:46.908+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 34
2025-08-19T10:11:46.944+08:00  INFO 1980 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 34
2025-08-19T10:11:46.945+08:00 DEBUG 1980 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:11:47.057+08:00  INFO 1980 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:11:47.058+08:00 DEBUG 1980 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:12:22.696+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:12:22.696+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:12:22.696+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 34, 用户ID: 4
2025-08-19T10:12:22.951+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 34
2025-08-19T10:12:23.026+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 34
2025-08-19T10:12:23.026+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 34
2025-08-19T10:12:23.064+08:00  INFO 1980 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 34
2025-08-19T10:12:23.065+08:00 DEBUG 1980 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:12:41.265+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:12:41.266+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:12:41.266+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 34, 用户ID: 4
2025-08-19T10:12:41.502+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 34
2025-08-19T10:12:41.570+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 34
2025-08-19T10:12:41.570+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 34
2025-08-19T10:12:41.604+08:00  INFO 1980 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 34
2025-08-19T10:12:41.605+08:00 DEBUG 1980 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:12:55.833+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:12:55.833+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34&userId=4
2025-08-19T10:12:55.833+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 34, 用户ID: 4
2025-08-19T10:12:56.068+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 34
2025-08-19T10:12:56.144+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 34
2025-08-19T10:12:56.144+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 34
2025-08-19T10:12:56.185+08:00  INFO 1980 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 34
2025-08-19T10:12:56.186+08:00 DEBUG 1980 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:13:08.520+08:00  INFO 1980 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:13:08.521+08:00  INFO 1980 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-19T10:13:08.706+08:00  INFO 1980 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-19T10:13:57.095+08:00  INFO 24460 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 24460 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T10:13:57.096+08:00 DEBUG 24460 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T10:13:57.097+08:00  INFO 24460 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T10:13:57.718+08:00  INFO 24460 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T10:13:57.847+08:00  INFO 24460 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 124 ms. Found 42 JPA repository interfaces.
2025-08-19T10:13:58.322+08:00  INFO 24460 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T10:13:58.330+08:00  INFO 24460 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T10:13:58.330+08:00  INFO 24460 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T10:13:58.369+08:00  INFO 24460 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T10:13:58.369+08:00  INFO 24460 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1242 ms
2025-08-19T10:13:58.444+08:00 DEBUG 24460 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T10:13:58.551+08:00  INFO 24460 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T10:13:58.599+08:00  INFO 24460 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T10:13:58.626+08:00  INFO 24460 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T10:13:58.707+08:00  INFO 24460 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T10:13:59.340+08:00  INFO 24460 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@4213bc3e
2025-08-19T10:13:59.342+08:00  INFO 24460 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T10:13:59.605+08:00  INFO 24460 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T10:14:00.850+08:00  INFO 24460 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T10:14:01.426+08:00  INFO 24460 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:14:01.621+08:00  INFO 24460 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T10:14:03.737+08:00  INFO 24460 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T10:14:03.860+08:00  INFO 24460 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T10:14:03.909+08:00  INFO 24460 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@9336bc5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1227ef86, org.springframework.security.web.context.SecurityContextHolderFilter@560c326f, org.springframework.security.web.header.HeaderWriterFilter@2b7c877a, org.springframework.web.filter.CorsFilter@4ec23ac, org.springframework.security.web.authentication.logout.LogoutFilter@3dab4c68, com.heating.filter.JwtAuthenticationFilter@7d484fcd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7486fa3f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1838cbb5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@13879c47, org.springframework.security.web.access.ExceptionTranslationFilter@27b72947, org.springframework.security.web.access.intercept.AuthorizationFilter@19a51054]
2025-08-19T10:14:04.263+08:00  INFO 24460 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T10:14:04.274+08:00  INFO 24460 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.548 seconds (process running for 8.073)
2025-08-19T10:14:19.853+08:00  INFO 24460 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-19T10:14:19.853+08:00  INFO 24460 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-19T10:14:19.855+08:00  INFO 24460 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-19T10:14:19.867+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:14:19.867+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:14:19.877+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=4&page=1&size=10
2025-08-19T10:14:19.877+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=4
2025-08-19T10:14:19.894+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 4
2025-08-19T10:14:19.895+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 4, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:14:20.195+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 7
2025-08-19T10:14:20.195+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 7, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T10:14:20.339+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共2条记录
2025-08-19T10:14:20.381+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:14:20.489+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 7, 总数: 2
2025-08-19T10:14:20.490+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:14:21.213+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:14:21.213+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=34
2025-08-19T10:14:21.214+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 34
2025-08-19T10:14:21.214+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 34
2025-08-19T10:14:21.332+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 34
2025-08-19T10:14:21.332+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 34
2025-08-19T10:14:21.369+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 34
2025-08-19T10:14:21.370+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:14:28.252+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=33
2025-08-19T10:14:28.252+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=33
2025-08-19T10:14:28.253+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 33
2025-08-19T10:14:28.253+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 33
2025-08-19T10:14:28.368+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 33
2025-08-19T10:14:28.368+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 33
2025-08-19T10:14:28.405+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 33
2025-08-19T10:14:28.406+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:15:08.063+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:15:08.064+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:15:08.101+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:15:08.102+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:15:08.442+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=partial_paid, 总金额=3365.17, 已缴金额=1009.55
2025-08-19T10:15:08.442+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: partial_paid
2025-08-19T10:15:08.442+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单部分缴费且不供暖，查询用热申请
2025-08-19T10:15:08.491+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到审批通过的用热申请，ID: 1, 金额: 2355.62
2025-08-19T10:15:08.491+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:15:08.491+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:15:08.933+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:15:08.933+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T10:15:08.972+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:15:08.972+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:15:09.011+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T10:15:09.012+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:15:09.012+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T10:15:09.012+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T10:15:09.024+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:01.957+08:00 DEBUG 24460 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment/online-pay
2025-08-19T10:16:01.957+08:00 DEBUG 24460 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment/online-pay
2025-08-19T10:16:01.960+08:00  INFO 24460 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 收到在线缴费请求: OnlinePaymentRequest(billId=7, houseId=7, amount=2355.62, paymentMethod=wechat, transactionNo=WX1755569761935868, isHeating=1, feeType=supplement, remark=微信小程序微信在线缴费 - 补交费用, userId=null)
2025-08-19T10:16:01.962+08:00  INFO 24460 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 请求详情 - billId: 7, houseId: 7, amount: 2355.62, paymentMethod: wechat, transactionNo: WX1755569761935868
2025-08-19T10:16:02.039+08:00  INFO 24460 --- [http-nio-8889-exec-5] c.h.service.impl.PaymentServiceImpl      : 开始处理在线缴费: OnlinePaymentRequest(billId=7, houseId=7, amount=2355.62, paymentMethod=wechat, transactionNo=WX1755569761935868, isHeating=1, feeType=supplement, remark=微信小程序微信在线缴费 - 补交费用, userId=null)
2025-08-19T10:16:02.229+08:00  INFO 24460 --- [http-nio-8889-exec-5] c.h.service.impl.PaymentServiceImpl      : 房屋用热状态更新成功: houseId=7, isHeating=1
2025-08-19T10:16:02.229+08:00  INFO 24460 --- [http-nio-8889-exec-5] c.h.service.impl.PaymentServiceImpl      : 在线缴费处理成功: paymentId=18, billId=7, amount=2355.62, isHeating=1
2025-08-19T10:16:02.387+08:00  INFO 24460 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 在线缴费处理成功: paymentId=18, billId=7
2025-08-19T10:16:02.388+08:00 DEBUG 24460 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:06.521+08:00 DEBUG 24460 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T10:16:06.523+08:00 DEBUG 24460 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T10:16:06.526+08:00 DEBUG 24460 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T10:16:06.526+08:00 DEBUG 24460 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T10:16:06.528+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T10:16:06.528+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T10:16:06.529+08:00  INFO 24460 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T10:16:06.529+08:00  INFO 24460 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T10:16:06.818+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询到2条缴费记录
2025-08-19T10:16:06.822+08:00 DEBUG 24460 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:06.837+08:00  INFO 24460 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询到2条缴费记录
2025-08-19T10:16:06.838+08:00 DEBUG 24460 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:09.352+08:00 DEBUG 24460 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/invoice-detail
2025-08-19T10:16:09.352+08:00 DEBUG 24460 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/invoice-detail
2025-08-19T10:16:09.358+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 获取票据详情: paymentId=18
2025-08-19T10:16:09.358+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 获取票据详情: paymentId=18
2025-08-19T10:16:10.103+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:16:10.104+08:00 DEBUG 24460 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:11.891+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T10:16:11.891+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T10:16:11.892+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=7
2025-08-19T10:16:11.892+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=7
2025-08-19T10:16:12.184+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询到2条缴费记录
2025-08-19T10:16:12.184+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:14.553+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:16:14.554+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:16:14.554+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:16:14.554+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:16:14.554+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:16:14.554+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:16:14.555+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:16:14.555+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:16:14.805+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:16:14.805+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:16:14.813+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:16:14.813+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:16:14.843+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:16:14.843+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:16:14.851+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:16:14.851+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:16:15.285+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:16:15.285+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:16:15.285+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:16:15.286+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:16:15.292+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:16:15.292+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:16:15.292+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:16:15.292+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:16:15.323+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:16:15.324+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:16:15.329+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:16:15.361+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:16:15.362+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:16:15.362+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:16:15.362+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:16:15.362+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:16:15.362+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:16:15.364+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:15.366+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:16:15.366+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:16:15.366+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:16:15.366+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:16:15.366+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:16:15.366+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:16:15.367+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:28.457+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:16:28.457+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:16:28.458+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:16:28.458+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:16:28.752+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=paid, 总金额=3365.17, 已缴金额=3365.17
2025-08-19T10:16:28.752+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:16:28.752+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:16:28.752+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:16:28.752+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:16:39.739+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:16:39.739+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:16:39.739+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:16:39.739+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:16:40.027+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=paid, 总金额=3365.17, 已缴金额=3365.17
2025-08-19T10:16:40.027+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:16:40.027+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:16:40.027+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:16:40.028+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:18:32.661+08:00 DEBUG 24460 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:18:32.662+08:00 DEBUG 24460 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:18:32.663+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:18:32.663+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:18:32.957+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=paid, 总金额=3365.17, 已缴金额=3365.17
2025-08-19T10:18:32.957+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:18:32.957+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:18:32.957+08:00  INFO 24460 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:18:32.957+08:00 DEBUG 24460 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:22:08.816+08:00 DEBUG 24460 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:22:08.816+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:22:08.816+08:00 DEBUG 24460 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:22:08.816+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:22:08.817+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:08.817+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:08.817+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:08.817+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:09.068+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:22:09.068+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:22:09.089+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:22:09.089+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:22:09.115+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:22:09.115+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:22:09.125+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:22:09.125+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:22:09.545+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:22:09.545+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:22:09.545+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:22:09.545+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:22:09.569+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:22:09.569+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:22:09.569+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:22:09.569+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:22:09.583+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:22:09.607+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:22:09.621+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:22:09.621+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:22:09.621+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:22:09.621+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:22:09.621+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:22:09.621+08:00  INFO 24460 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:22:09.622+08:00 DEBUG 24460 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:22:09.644+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:22:09.644+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:22:09.645+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:22:09.645+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:22:09.645+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:22:09.645+08:00  INFO 24460 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:22:09.645+08:00 DEBUG 24460 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:22:19.907+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:22:19.908+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:22:19.908+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:19.908+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:19.909+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:22:19.909+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:22:19.910+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:19.910+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:20.178+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:22:20.178+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:22:20.193+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:22:20.193+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:22:20.229+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:22:20.229+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:22:20.230+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:22:20.230+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:22:20.659+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:22:20.659+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:22:20.659+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:22:20.659+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:22:20.672+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:22:20.672+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:22:20.672+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:22:20.672+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:22:20.702+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:22:20.709+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:22:20.739+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:22:20.739+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:22:20.739+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:22:20.739+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:22:20.739+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:22:20.740+08:00  INFO 24460 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:22:20.740+08:00 DEBUG 24460 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:22:20.747+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:22:20.747+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:22:20.747+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:22:20.747+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:22:20.747+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:22:20.747+08:00  INFO 24460 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:22:20.748+08:00 DEBUG 24460 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:22:26.059+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:22:26.059+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:22:26.059+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:22:26.059+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:22:26.060+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:26.060+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:26.060+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:26.060+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:22:26.323+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:22:26.323+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:22:26.325+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:22:26.325+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:22:26.360+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:22:26.360+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:22:26.373+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:22:26.373+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:22:26.827+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:22:26.827+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:22:26.827+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:22:26.827+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:22:26.841+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:22:26.841+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:22:26.841+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:22:26.841+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:22:26.864+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:22:26.878+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:22:26.901+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:22:26.901+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:22:26.901+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:22:26.901+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:22:26.901+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:22:26.901+08:00  INFO 24460 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:22:26.902+08:00 DEBUG 24460 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:22:26.915+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:22:26.915+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:22:26.915+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:22:26.915+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:22:26.915+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:22:26.915+08:00  INFO 24460 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:22:26.916+08:00 DEBUG 24460 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:22:28.133+08:00  INFO 24460 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:22:28.134+08:00  INFO 24460 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-19T10:22:28.334+08:00  INFO 24460 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-19T10:22:33.503+08:00  INFO 22680 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 22680 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T10:22:33.504+08:00 DEBUG 22680 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T10:22:33.504+08:00  INFO 22680 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T10:22:34.094+08:00  INFO 22680 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T10:22:34.234+08:00  INFO 22680 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 134 ms. Found 42 JPA repository interfaces.
2025-08-19T10:22:34.766+08:00  INFO 22680 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T10:22:34.777+08:00  INFO 22680 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T10:22:34.777+08:00  INFO 22680 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T10:22:34.819+08:00  INFO 22680 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T10:22:34.820+08:00  INFO 22680 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1285 ms
2025-08-19T10:22:34.893+08:00 DEBUG 22680 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T10:22:34.997+08:00  INFO 22680 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T10:22:35.054+08:00  INFO 22680 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T10:22:35.084+08:00  INFO 22680 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T10:22:35.176+08:00  INFO 22680 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T10:22:35.862+08:00  INFO 22680 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5534e6f1
2025-08-19T10:22:35.863+08:00  INFO 22680 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T10:22:36.130+08:00  INFO 22680 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T10:22:37.715+08:00  INFO 22680 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T10:22:38.367+08:00  INFO 22680 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:22:38.594+08:00  INFO 22680 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T10:22:40.632+08:00  INFO 22680 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T10:22:40.770+08:00  INFO 22680 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T10:22:40.811+08:00  INFO 22680 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2235ab04, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c38bb38, org.springframework.security.web.context.SecurityContextHolderFilter@2aad05e0, org.springframework.security.web.header.HeaderWriterFilter@e50c1f6, org.springframework.web.filter.CorsFilter@6e330ce7, org.springframework.security.web.authentication.logout.LogoutFilter@27b611aa, com.heating.filter.JwtAuthenticationFilter@125f16b2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18435ed6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@38e2e99e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@25757934, org.springframework.security.web.access.ExceptionTranslationFilter@6b993d8b, org.springframework.security.web.access.intercept.AuthorizationFilter@2dae6c7c]
2025-08-19T10:22:41.124+08:00  INFO 22680 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T10:22:41.132+08:00  INFO 22680 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.973 seconds (process running for 8.485)
2025-08-19T10:22:56.229+08:00  INFO 22680 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-19T10:22:56.229+08:00  INFO 22680 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-19T10:22:56.230+08:00  INFO 22680 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-19T10:22:56.245+08:00 DEBUG 22680 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:22:56.251+08:00 DEBUG 22680 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:22:56.339+08:00  INFO 22680 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:22:56.339+08:00  INFO 22680 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:22:56.754+08:00  INFO 22680 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=paid, 总金额=3365.17, 已缴金额=3365.17
2025-08-19T10:22:56.755+08:00  INFO 22680 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:22:56.755+08:00  INFO 22680 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:22:56.755+08:00  INFO 22680 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:22:56.774+08:00 DEBUG 22680 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:23:01.455+08:00 DEBUG 22680 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:23:01.455+08:00 DEBUG 22680 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:23:01.455+08:00 DEBUG 22680 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:23:01.455+08:00 DEBUG 22680 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:23:01.456+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:23:01.456+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:23:01.457+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:23:01.457+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:23:01.693+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:23:01.693+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:23:01.705+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:23:01.705+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:23:01.736+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:23:01.737+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:23:01.744+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:23:01.744+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:23:02.156+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:23:02.157+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:23:02.157+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:23:02.157+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:23:02.193+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:23:02.193+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:23:02.193+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:23:02.193+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:23:02.194+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:23:02.194+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:23:02.195+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:23:02.195+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:23:02.195+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:23:02.195+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:23:02.195+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:23:02.195+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:23:02.196+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:23:02.231+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:23:02.241+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:23:02.242+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:23:02.242+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:23:02.242+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:23:02.242+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:23:02.242+08:00  INFO 22680 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:23:02.256+08:00 DEBUG 22680 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:23:02.269+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:23:02.269+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:23:02.269+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:23:02.269+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:23:02.269+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:23:02.269+08:00  INFO 22680 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:23:02.270+08:00 DEBUG 22680 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:24:36.376+08:00 DEBUG 22680 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:24:36.377+08:00 DEBUG 22680 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:24:36.377+08:00 DEBUG 22680 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:24:36.378+08:00 DEBUG 22680 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:24:39.649+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:24:39.649+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:24:39.649+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:24:40.897+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:24:41.085+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:24:41.085+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:24:41.121+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:24:41.122+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:24:41.152+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:24:41.152+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:24:41.195+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:24:41.195+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:24:41.549+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:24:41.549+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:24:41.549+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:24:41.550+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:24:41.586+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:24:41.624+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:24:41.624+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:24:41.624+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:24:41.624+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:24:41.624+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:24:41.624+08:00  INFO 22680 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:24:41.624+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:24:41.625+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:24:41.625+08:00 DEBUG 22680 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:24:41.625+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:24:41.625+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:24:41.667+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:24:41.667+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:24:41.667+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:24:41.668+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:24:41.668+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:24:41.668+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:24:41.669+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:24:41.669+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:24:41.669+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:24:41.713+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:24:41.714+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:24:41.714+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:24:41.715+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:24:41.715+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:24:42.144+08:00  INFO 22680 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:25:32.165+08:00  WARN 22680 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Thread starvation or clock leap detected (housekeeper delta=56s184ms649µs700ns).
2025-08-19T10:25:32.168+08:00 DEBUG 22680 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:25:57.806+08:00 DEBUG 22680 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:25:57.806+08:00 DEBUG 22680 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:25:57.806+08:00 DEBUG 22680 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:25:57.806+08:00 DEBUG 22680 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:25:59.201+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:25:59.202+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:25:59.202+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:25:59.202+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:25:59.452+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:25:59.452+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:25:59.466+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:25:59.466+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:25:59.486+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:25:59.487+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:25:59.502+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:25:59.502+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:25:59.930+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:25:59.930+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:25:59.930+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:25:59.930+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:25:59.981+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:25:59.996+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:25:59.996+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:25:59.996+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:25:59.996+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:26:00.025+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:26:00.026+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:26:00.026+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:26:00.026+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:26:00.026+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:26:00.026+08:00  INFO 22680 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:26:00.027+08:00 DEBUG 22680 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:26:00.033+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:26:00.034+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:26:00.035+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:26:00.035+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:26:00.035+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:26:00.035+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:26:00.035+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:26:00.035+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:26:00.035+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:26:00.074+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:26:00.074+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:26:00.074+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:26:00.074+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:26:00.074+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:26:00.074+08:00  INFO 22680 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:26:00.075+08:00 DEBUG 22680 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:27:03.577+08:00  INFO 22680 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:27:03.578+08:00  INFO 22680 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-19T10:27:03.761+08:00  INFO 22680 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-19T10:27:09.969+08:00  INFO 25420 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 25420 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T10:27:09.970+08:00 DEBUG 25420 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T10:27:09.970+08:00  INFO 25420 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T10:27:10.576+08:00  INFO 25420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T10:27:10.696+08:00  INFO 25420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 114 ms. Found 42 JPA repository interfaces.
2025-08-19T10:27:11.185+08:00  INFO 25420 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T10:27:11.194+08:00  INFO 25420 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T10:27:11.194+08:00  INFO 25420 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T10:27:11.232+08:00  INFO 25420 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T10:27:11.232+08:00  INFO 25420 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1228 ms
2025-08-19T10:27:11.284+08:00 DEBUG 25420 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T10:27:11.385+08:00  INFO 25420 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T10:27:11.430+08:00  INFO 25420 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T10:27:11.457+08:00  INFO 25420 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T10:27:11.540+08:00  INFO 25420 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T10:27:12.154+08:00  INFO 25420 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@35f760a4
2025-08-19T10:27:12.156+08:00  INFO 25420 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T10:27:12.429+08:00  INFO 25420 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T10:27:13.657+08:00  INFO 25420 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T10:27:14.214+08:00  INFO 25420 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:27:14.427+08:00  INFO 25420 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T10:27:16.330+08:00  INFO 25420 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T10:27:16.426+08:00  INFO 25420 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T10:27:16.460+08:00  INFO 25420 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3ae8f170, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@59c81e46, org.springframework.security.web.context.SecurityContextHolderFilter@479a1d7a, org.springframework.security.web.header.HeaderWriterFilter@608019af, org.springframework.web.filter.CorsFilter@19a51054, org.springframework.security.web.authentication.logout.LogoutFilter@75332dcf, com.heating.filter.JwtAuthenticationFilter@e3899fd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@76d04a25, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2b7c877a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5c98f2ef, org.springframework.security.web.access.ExceptionTranslationFilter@42e1aecb, org.springframework.security.web.access.intercept.AuthorizationFilter@6d32e212]
2025-08-19T10:27:16.748+08:00  INFO 25420 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T10:27:16.756+08:00  INFO 25420 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.203 seconds (process running for 7.682)
2025-08-19T10:28:07.055+08:00  INFO 25420 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:28:07.056+08:00  INFO 25420 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-19T10:28:07.228+08:00  INFO 25420 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-19T10:28:12.309+08:00  INFO 22940 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 22940 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T10:28:12.310+08:00 DEBUG 22940 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T10:28:12.311+08:00  INFO 22940 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T10:28:12.879+08:00  INFO 22940 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T10:28:13.002+08:00  INFO 22940 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 118 ms. Found 42 JPA repository interfaces.
2025-08-19T10:28:13.498+08:00  INFO 22940 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T10:28:13.506+08:00  INFO 22940 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T10:28:13.506+08:00  INFO 22940 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T10:28:13.546+08:00  INFO 22940 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T10:28:13.546+08:00  INFO 22940 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1206 ms
2025-08-19T10:28:13.603+08:00 DEBUG 22940 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T10:28:13.703+08:00  INFO 22940 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T10:28:13.750+08:00  INFO 22940 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T10:28:13.778+08:00  INFO 22940 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T10:28:13.866+08:00  INFO 22940 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T10:28:14.471+08:00  INFO 22940 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@4c6fc3e7
2025-08-19T10:28:14.472+08:00  INFO 22940 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T10:28:14.737+08:00  INFO 22940 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T10:28:16.064+08:00  INFO 22940 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T10:28:16.620+08:00  INFO 22940 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T10:28:16.805+08:00  INFO 22940 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T10:28:18.682+08:00  INFO 22940 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T10:28:18.778+08:00  INFO 22940 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T10:28:18.814+08:00  INFO 22940 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4ebe717f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@59920243, org.springframework.security.web.context.SecurityContextHolderFilter@29722da7, org.springframework.security.web.header.HeaderWriterFilter@68fbbb93, org.springframework.web.filter.CorsFilter@637a665a, org.springframework.security.web.authentication.logout.LogoutFilter@178a0b1, com.heating.filter.JwtAuthenticationFilter@13192275, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@f76dae9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e2245f0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6917b034, org.springframework.security.web.access.ExceptionTranslationFilter@717a02a2, org.springframework.security.web.access.intercept.AuthorizationFilter@72ec0943]
2025-08-19T10:28:19.105+08:00  INFO 22940 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T10:28:19.114+08:00  INFO 22940 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.138 seconds (process running for 7.604)
2025-08-19T10:28:24.960+08:00  INFO 22940 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-19T10:28:24.960+08:00  INFO 22940 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-19T10:28:24.961+08:00  INFO 22940 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-19T10:28:24.973+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:28:24.973+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:28:24.980+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:28:24.980+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:28:25.042+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:28:25.042+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:28:25.042+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:28:25.042+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T10:28:25.320+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:28:25.320+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:28:25.324+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=1
2025-08-19T10:28:25.324+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:28:25.419+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:28:25.419+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:28:25.419+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:28:25.419+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T10:28:25.818+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:28:25.818+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:28:25.819+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:28:25.819+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:28:25.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T10:28:25.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:28:25.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=3365.17, 欠费金额=0.00, 状态=paid
2025-08-19T10:28:25.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:28:25.857+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:28:25.857+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:28:25.857+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:28:25.857+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:28:25.857+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:28:25.857+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:28:25.858+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:28:25.858+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:28:25.858+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:28:25.865+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:28:25.865+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:28:25.865+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.17 元
2025-08-19T10:28:25.865+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:28:25.866+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.17 元
2025-08-19T10:28:25.866+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.17 元
2025-08-19T10:28:25.866+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:28:25.866+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:28:25.866+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T10:28:25.905+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:28:25.905+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=18, 金额=2355.62, 方式=微信支付, 日期=2025-08-19 10:16
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:28:25.906+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:28:25.941+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:28:25.941+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:28:50.099+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:28:50.099+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:28:50.100+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:28:50.100+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:28:50.361+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=paid, 总金额=3365.17, 已缴金额=3365.17
2025-08-19T10:28:50.361+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:28:50.361+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:28:50.361+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:28:50.362+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:29:05.201+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:29:05.202+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:29:05.202+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=7, heatingYear=2025
2025-08-19T10:29:05.203+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=7, heatingYear=2025
2025-08-19T10:29:05.515+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=7, 状态=paid, 总金额=3365.17, 已缴金额=3365.17
2025-08-19T10:29:05.515+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:29:05.515+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:29:05.516+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:29:05.516+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:29:34.974+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/login
2025-08-19T10:29:34.975+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/login
2025-08-19T10:29:35.968+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=13
2025-08-19T10:29:36.358+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=13, heatUnitId=1, communityName=印象小区
2025-08-19T10:29:36.360+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:29:39.885+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:29:39.885+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:29:39.887+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:29:39.887+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:29:40.144+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:29:40.144+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:29:40.144+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:29:40.144+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:29:40.145+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:33:23.955+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:33:23.956+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:33:23.956+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:33:23.957+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:33:24.227+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:33:24.228+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:33:24.228+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:33:24.228+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:33:24.228+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:33:48.633+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:33:48.634+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:33:48.635+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:33:48.635+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:33:48.916+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:33:48.916+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:33:48.916+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:33:48.916+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:33:48.917+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:34:37.099+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:34:37.099+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:34:37.101+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:34:37.101+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:34:37.382+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:34:37.382+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:34:37.382+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:34:37.382+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:34:37.383+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:34:54.922+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:34:54.923+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:34:54.925+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:34:54.925+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:34:55.215+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:34:55.215+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:34:55.215+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:34:55.215+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:34:55.215+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:35:27.324+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:35:27.324+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:35:27.325+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:35:27.326+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:35:27.593+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:35:27.593+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:35:27.593+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:35:27.593+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:35:27.594+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:35:50.391+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:35:50.391+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:35:50.392+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:35:50.392+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:35:50.657+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:35:50.657+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:35:50.657+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:35:50.657+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:35:50.658+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:37:07.771+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:37:07.771+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:37:07.773+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:37:07.773+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:37:08.041+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:37:08.041+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:37:08.041+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:37:08.041+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:37:08.041+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:45:09.713+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:45:09.713+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:45:09.714+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:45:09.714+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:45:09.985+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:45:09.985+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:45:09.985+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:45:09.985+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:45:09.986+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:45:26.401+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:45:26.401+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:45:26.402+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:45:26.402+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:45:26.662+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:45:26.662+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:45:26.663+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:45:26.663+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:45:26.663+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:49:28.393+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:49:28.393+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:49:28.393+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:49:28.393+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:49:28.397+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:49:28.397+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:49:28.398+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:49:28.398+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:49:28.620+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=13, 户号=HT2024000131, 用热状态=1
2025-08-19T10:49:28.620+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:49:28.635+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=13, 户号=HT2024000131, 用热状态=1
2025-08-19T10:49:28.636+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:49:28.656+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:49:28.656+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=13
2025-08-19T10:49:28.671+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:49:28.671+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=13
2025-08-19T10:49:29.054+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=13, heatUnitId=1, communityName=印象小区
2025-08-19T10:49:29.054+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:49:29.054+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=13, 总金额=3365.16, 已缴金额=3365.16, 欠费金额=0.00, 状态=paid
2025-08-19T10:49:29.054+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:49:29.078+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=13, heatUnitId=1, communityName=印象小区
2025-08-19T10:49:29.078+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:49:29.078+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=13, 总金额=3365.16, 已缴金额=3365.16, 欠费金额=0.00, 状态=paid
2025-08-19T10:49:29.078+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:49:29.086+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.16 元
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.16 元
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.16 元
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:49:29.087+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 13
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.16 元
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.16 元
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.16 元
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:49:29.112+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 13
2025-08-19T10:49:29.120+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T10:49:29.120+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=13, 金额=3365.16, 方式=微信支付, 日期=2025-08-14 10:14
2025-08-19T10:49:29.120+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T10:49:29.120+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:49:29.120+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:49:29.121+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:49:29.150+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T10:49:29.150+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=13, 金额=3365.16, 方式=微信支付, 日期=2025-08-14 10:14
2025-08-19T10:49:29.150+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T10:49:29.150+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:49:29.150+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:49:29.151+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:56:15.018+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:56:15.018+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:56:15.018+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:56:15.018+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:56:15.289+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:56:15.289+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:56:15.289+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:56:15.289+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:56:15.289+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:57:14.016+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T10:57:14.016+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T10:57:14.017+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T10:57:14.017+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T10:57:14.284+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T10:57:14.284+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T10:57:14.284+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T10:57:14.284+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T10:57:14.284+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:57:42.008+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/data/5
2025-08-19T10:57:42.008+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/data/5
2025-08-19T10:57:42.078+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:58:00.591+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:58:00.591+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:58:00.592+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:58:00.592+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:58:00.592+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T10:58:00.593+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T10:58:00.593+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:58:00.593+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T10:58:00.817+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=13, 户号=HT2024000131, 用热状态=1
2025-08-19T10:58:00.817+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:58:00.847+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=13, 户号=HT2024000131, 用热状态=1
2025-08-19T10:58:00.847+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T10:58:00.851+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:58:00.851+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=13
2025-08-19T10:58:00.885+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T10:58:00.885+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=13
2025-08-19T10:58:01.236+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=13, heatUnitId=1, communityName=印象小区
2025-08-19T10:58:01.236+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:58:01.236+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=13, 总金额=3365.16, 已缴金额=3365.16, 欠费金额=0.00, 状态=paid
2025-08-19T10:58:01.236+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:58:01.269+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.16 元
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.16 元
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.16 元
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:58:01.270+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 13
2025-08-19T10:58:01.303+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T10:58:01.303+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=13, 金额=3365.16, 方式=微信支付, 日期=2025-08-14 10:14
2025-08-19T10:58:01.303+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T10:58:01.303+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:58:01.303+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:58:01.304+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T10:58:01.322+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=13, heatUnitId=1, communityName=印象小区
2025-08-19T10:58:01.322+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T10:58:01.322+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=13, 总金额=3365.16, 已缴金额=3365.16, 欠费金额=0.00, 状态=paid
2025-08-19T10:58:01.322+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T10:58:01.360+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T10:58:01.360+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T10:58:01.360+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.16 元
2025-08-19T10:58:01.360+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T10:58:01.360+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.16 元
2025-08-19T10:58:01.361+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.16 元
2025-08-19T10:58:01.361+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T10:58:01.361+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T10:58:01.361+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 13
2025-08-19T10:58:01.398+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T10:58:01.398+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=13, 金额=3365.16, 方式=微信支付, 日期=2025-08-14 10:14
2025-08-19T10:58:01.398+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T10:58:01.398+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T10:58:01.398+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T10:58:01.399+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:00:53.968+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T11:00:53.968+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T11:00:53.969+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T11:00:53.969+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T11:00:54.238+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T11:00:54.238+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T11:00:54.238+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T11:00:54.238+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T11:00:54.239+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:01:05.104+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T11:01:05.104+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T11:01:05.105+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=13, heatingYear=2025
2025-08-19T11:01:05.105+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=13, heatingYear=2025
2025-08-19T11:01:05.373+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=13, 状态=paid, 总金额=3365.16, 已缴金额=3365.16
2025-08-19T11:01:05.373+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T11:01:05.373+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T11:01:05.373+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T11:01:05.374+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:14.927+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T11:04:14.927+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T11:04:14.930+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=13
2025-08-19T11:04:14.930+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=13
2025-08-19T11:04:14.932+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T11:04:14.932+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T11:04:14.933+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=13
2025-08-19T11:04:14.933+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=13
2025-08-19T11:04:15.217+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T11:04:15.220+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:15.227+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T11:04:15.228+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:16.863+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/invoice-detail
2025-08-19T11:04:16.864+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/invoice-detail
2025-08-19T11:04:16.870+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 获取票据详情: paymentId=13
2025-08-19T11:04:16.870+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 获取票据详情: paymentId=13
2025-08-19T11:04:17.559+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:04:17.561+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:19.482+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T11:04:19.483+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T11:04:19.483+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=13
2025-08-19T11:04:19.484+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=13
2025-08-19T11:04:19.761+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询到1条缴费记录
2025-08-19T11:04:19.763+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:34.048+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:04:34.048+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:04:34.054+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:04:34.054+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:04:34.056+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 5
2025-08-19T11:04:34.057+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:04:34.289+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:04:34.294+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 13
2025-08-19T11:04:34.410+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T11:04:34.411+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:34.552+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 13, 总数: 0
2025-08-19T11:04:34.553+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:35.501+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T11:04:35.502+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T11:04:35.509+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T11:04:35.758+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T11:04:35.826+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T11:04:35.826+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:36.230+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10&status=%E5%B7%B2%E7%A1%AE%E8%AE%A4
2025-08-19T11:04:36.230+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10&status=%E5%B7%B2%E7%A1%AE%E8%AE%A4
2025-08-19T11:04:36.230+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: 已确认
2025-08-19T11:04:36.442+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: 已确认
2025-08-19T11:04:36.510+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T11:04:36.511+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:36.616+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T11:04:36.616+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10&status=%E5%BE%85%E7%A1%AE%E8%AE%A4
2025-08-19T11:04:36.617+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T11:04:36.818+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: 待确认
2025-08-19T11:04:36.886+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T11:04:36.887+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:36.996+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:04:36.996+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:04:36.997+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:04:37.766+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:04:37.835+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T11:04:37.835+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:42.093+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:04:42.093+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:04:42.094+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 5
2025-08-19T11:04:42.095+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:04:42.095+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:04:42.096+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:04:42.327+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 13
2025-08-19T11:04:42.327+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:04:42.402+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T11:04:42.402+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:42.537+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 13, 总数: 0
2025-08-19T11:04:42.538+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:04:43.420+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/data/5
2025-08-19T11:04:43.421+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/data/5
2025-08-19T11:04:43.491+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:05:08.204+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/report
2025-08-19T11:05:08.204+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/report
2025-08-19T11:05:08.214+08:00  INFO 22940 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 上报故障: FaultReportRequest(heatUnitId=1, houseId=13, alarmId=null, faultType=设备故障, faultSource=用户投诉, faultLevel=一般, faultDesc=设备无法使用，请检查, faultStatus=null, occurTime=2025-08-19T11:04, reportUserId=5, repairtUserId=0, address=印象小区 1-1-0301, managerId=null, attachment=[])
2025-08-19T11:05:08.405+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:05:10.976+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:05:10.976+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:05:10.976+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 5
2025-08-19T11:05:10.979+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:05:10.979+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:05:10.980+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:05:11.209+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 13
2025-08-19T11:05:11.212+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:05:11.282+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共1条记录
2025-08-19T11:05:11.283+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:05:11.424+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 13, 总数: 1
2025-08-19T11:05:11.424+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:05:13.057+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=35
2025-08-19T11:05:13.057+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=35
2025-08-19T11:05:13.057+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 35
2025-08-19T11:05:13.057+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 35
2025-08-19T11:05:13.161+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 35
2025-08-19T11:05:13.161+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 35
2025-08-19T11:05:13.196+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 35
2025-08-19T11:05:13.197+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:08.340+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:19:08.341+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:19:08.341+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T11:19:08.342+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T11:19:08.348+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:19:08.348+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:19:08.349+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T11:19:08.349+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=13, heatingYear=2025)
2025-08-19T11:19:08.599+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=13, 户号=HT2024000131, 用热状态=1
2025-08-19T11:19:08.599+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:19:08.611+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=13, 户号=HT2024000131, 用热状态=1
2025-08-19T11:19:08.611+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:19:08.632+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:19:08.632+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=13
2025-08-19T11:19:08.648+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:19:08.648+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=13
2025-08-19T11:19:09.056+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=13, heatUnitId=1, communityName=印象小区
2025-08-19T11:19:09.056+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:19:09.056+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=13, 总金额=3365.16, 已缴金额=3365.16, 欠费金额=0.00, 状态=paid
2025-08-19T11:19:09.056+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:19:09.064+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=13, heatUnitId=1, communityName=印象小区
2025-08-19T11:19:09.064+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:19:09.064+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=13, 总金额=3365.16, 已缴金额=3365.16, 欠费金额=0.00, 状态=paid
2025-08-19T11:19:09.064+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.16 元
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.16 元
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.16 元
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:19:09.091+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 13
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3365.16 元
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3365.16 元
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3365.16 元
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:19:09.102+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 13
2025-08-19T11:19:09.126+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T11:19:09.126+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=13, 金额=3365.16, 方式=微信支付, 日期=2025-08-14 10:14
2025-08-19T11:19:09.126+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T11:19:09.126+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:19:09.126+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:19:09.127+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:09.143+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T11:19:09.143+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=13, 金额=3365.16, 方式=微信支付, 日期=2025-08-14 10:14
2025-08-19T11:19:09.143+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T11:19:09.143+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:19:09.143+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:19:09.144+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:17.065+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:19:17.065+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=5
2025-08-19T11:19:17.066+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 5
2025-08-19T11:19:17.070+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:19:17.071+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=5&page=1&size=10
2025-08-19T11:19:17.071+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 5, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:19:17.308+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 13, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:19:17.319+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 13
2025-08-19T11:19:17.376+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共1条记录
2025-08-19T11:19:17.377+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:17.547+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 13, 总数: 1
2025-08-19T11:19:17.548+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:19.443+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-detail?faultId=35
2025-08-19T11:19:19.444+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-detail?faultId=35
2025-08-19T11:19:19.444+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 获取故障详情，故障ID: 35
2025-08-19T11:19:19.444+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取故障详情，故障ID: 35
2025-08-19T11:19:19.551+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 获取故障详情成功，故障ID: 35
2025-08-19T11:19:19.551+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息，故障ID: 35
2025-08-19T11:19:19.591+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 获取故障跟踪信息成功，故障ID: 35
2025-08-19T11:19:19.592+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:23.451+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bills
2025-08-19T11:19:23.452+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bills
2025-08-19T11:19:23.453+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 获取账单列表请求: BillListRequest(userId=5, houseId=null, heatYear=null, status=null, page=1, pageSize=20)
2025-08-19T11:19:23.458+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bills
2025-08-19T11:19:23.458+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bills
2025-08-19T11:19:23.458+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取账单列表请求: BillListRequest(userId=5, houseId=null, heatYear=null, status=null, page=1, pageSize=20)
2025-08-19T11:19:23.700+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 获取账单列表请求参数: BillListRequest(userId=5, houseId=13, heatYear=null, status=null, page=1, pageSize=20)
2025-08-19T11:19:23.716+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 获取账单列表请求参数: BillListRequest(userId=5, houseId=13, heatYear=null, status=null, page=1, pageSize=20)
2025-08-19T11:19:23.899+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到房屋信息: HT2024000131
2025-08-19T11:19:23.916+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到房屋信息: HT2024000131
2025-08-19T11:19:23.956+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 房屋ID=13下共有1条账单
2025-08-19T11:19:23.956+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋ID=13下共有1条账单
2025-08-19T11:19:23.956+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询所有账单，房屋ID: 13
2025-08-19T11:19:23.956+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询所有账单，房屋ID: 13
2025-08-19T11:19:23.997+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 查询结果: 总数=1, 当前页数据=1
2025-08-19T11:19:23.997+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询结果: 总数=1, 当前页数据=1
2025-08-19T11:19:23.998+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 处理账单: ID=13, 年份=2025, 状态=paid
2025-08-19T11:19:23.998+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 处理账单: ID=13, 年份=2025, 状态=paid
2025-08-19T11:19:24.032+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:24.033+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:24.069+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 返回账单列表: 总数=1, 当前页=1
2025-08-19T11:19:24.071+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:24.073+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 返回账单列表: 总数=1, 当前页=1
2025-08-19T11:19:24.074+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:25.818+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill-detail
2025-08-19T11:19:25.818+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill-detail
2025-08-19T11:19:25.820+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payments
2025-08-19T11:19:25.820+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payments
2025-08-19T11:19:25.820+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取缴费记录请求: BillDetailRequest(billId=13, userId=null, houseId=null)
2025-08-19T11:19:25.819+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 获取账单详情请求: BillDetailRequest(billId=13, userId=5, houseId=null)
2025-08-19T11:19:25.821+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 开始查询账单ID为13的缴费记录
2025-08-19T11:19:25.821+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 根据账单ID获取缴费记录: billId=13
2025-08-19T11:19:26.060+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 获取账单详情: BillDetailRequest(billId=13, userId=5, houseId=null)
2025-08-19T11:19:26.102+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 查询到1条缴费记录
2025-08-19T11:19:26.103+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:26.523+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:26.523+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:31.864+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bills
2025-08-19T11:19:31.864+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bills
2025-08-19T11:19:31.865+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 获取账单列表请求: BillListRequest(userId=5, houseId=null, heatYear=null, status=null, page=1, pageSize=20)
2025-08-19T11:19:32.102+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 获取账单列表请求参数: BillListRequest(userId=5, houseId=13, heatYear=null, status=null, page=1, pageSize=20)
2025-08-19T11:19:32.317+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到房屋信息: HT2024000131
2025-08-19T11:19:32.351+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋ID=13下共有1条账单
2025-08-19T11:19:32.351+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询所有账单，房屋ID: 13
2025-08-19T11:19:32.386+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询结果: 总数=1, 当前页数据=1
2025-08-19T11:19:32.386+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 处理账单: ID=13, 年份=2025, 状态=paid
2025-08-19T11:19:32.421+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:32.455+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 返回账单列表: 总数=1, 当前页=1
2025-08-19T11:19:32.456+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:48.190+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/login
2025-08-19T11:19:48.190+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/login
2025-08-19T11:19:49.134+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=1
2025-08-19T11:19:49.554+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=1, heatUnitId=1, communityName=印象小区
2025-08-19T11:19:49.555+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:53.097+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:19:53.097+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:19:53.097+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=1, heatingYear=2025)
2025-08-19T11:19:53.097+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=1, heatingYear=2025)
2025-08-19T11:19:53.099+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:19:53.099+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:19:53.100+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=1, heatingYear=2025)
2025-08-19T11:19:53.100+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=1, heatingYear=2025)
2025-08-19T11:19:53.334+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=1, 户号=HT2024000123, 用热状态=1
2025-08-19T11:19:53.334+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:19:53.353+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=1, 户号=HT2024000123, 用热状态=1
2025-08-19T11:19:53.353+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:19:53.371+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:19:53.371+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=1
2025-08-19T11:19:53.395+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:19:53.395+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=1
2025-08-19T11:19:53.800+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=1, heatUnitId=1, communityName=印象小区
2025-08-19T11:19:53.800+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:19:53.800+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=1, 总金额=3355.30, 已缴金额=3355.30, 欠费金额=0.00, 状态=paid
2025-08-19T11:19:53.800+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3355.30 元
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3355.30 元
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3355.30 元
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:19:53.836+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 1
2025-08-19T11:19:53.845+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=1, heatUnitId=1, communityName=印象小区
2025-08-19T11:19:53.845+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:19:53.845+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=1, 总金额=3355.30, 已缴金额=3355.30, 欠费金额=0.00, 状态=paid
2025-08-19T11:19:53.845+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:19:53.873+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T11:19:53.873+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=2, 金额=3355.30, 方式=微信支付, 日期=2025-07-16 00:00
2025-08-19T11:19:53.873+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T11:19:53.873+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:19:53.873+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:19:53.873+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:19:53.883+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3355.30 元
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3355.30 元
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3355.30 元
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:19:53.884+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 1
2025-08-19T11:19:53.919+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T11:19:53.919+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=2, 金额=3355.30, 方式=微信支付, 日期=2025-07-16 00:00
2025-08-19T11:19:53.919+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T11:19:53.919+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:19:53.919+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:19:53.920+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:09.677+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/login
2025-08-19T11:20:09.677+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/login
2025-08-19T11:20:10.605+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=2
2025-08-19T11:20:11.055+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=2, heatUnitId=1, communityName=印象小区
2025-08-19T11:20:11.055+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:16.326+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:20:16.326+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:20:16.327+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:16.327+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:16.328+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:20:16.328+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:20:16.329+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:16.329+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:16.569+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=2, 户号=HT2024000124, 用热状态=1
2025-08-19T11:20:16.569+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:20:16.572+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=2, 户号=HT2024000124, 用热状态=1
2025-08-19T11:20:16.572+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:20:16.603+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:20:16.603+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=2
2025-08-19T11:20:16.606+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:20:16.606+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=2
2025-08-19T11:20:17.035+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=2, heatUnitId=1, communityName=印象小区
2025-08-19T11:20:17.035+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:20:17.035+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=2, 总金额=3621.23, 已缴金额=3621.23, 欠费金额=0.00, 状态=paid
2025-08-19T11:20:17.035+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:20:17.051+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=2, heatUnitId=1, communityName=印象小区
2025-08-19T11:20:17.051+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:20:17.051+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=2, 总金额=3621.23, 已缴金额=3621.23, 欠费金额=0.00, 状态=paid
2025-08-19T11:20:17.051+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3621.23 元
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3621.23 元
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3621.23 元
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:20:17.073+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 2
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3621.23 元
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3621.23 元
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3621.23 元
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:20:17.090+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 2
2025-08-19T11:20:17.108+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T11:20:17.108+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=3, 金额=2000.00, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:17.108+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=4, 金额=1621.23, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:17.108+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T11:20:17.108+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:20:17.108+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:20:17.108+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:17.152+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T11:20:17.153+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=3, 金额=2000.00, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:17.153+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=4, 金额=1621.23, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:17.153+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T11:20:17.153+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:20:17.153+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:20:17.153+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:21.408+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T11:20:21.408+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T11:20:21.409+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=2, heatingYear=2025
2025-08-19T11:20:21.409+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=2, heatingYear=2025
2025-08-19T11:20:21.706+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=2, 状态=paid, 总金额=3621.23, 已缴金额=3621.23
2025-08-19T11:20:21.707+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: paid
2025-08-19T11:20:21.707+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 账单已全额缴费，返回空
2025-08-19T11:20:21.707+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=没有待缴账单
2025-08-19T11:20:21.707+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:24.194+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T11:20:24.194+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T11:20:24.195+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=2
2025-08-19T11:20:24.195+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=2
2025-08-19T11:20:24.196+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/payment-records
2025-08-19T11:20:24.196+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/payment-records
2025-08-19T11:20:24.197+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 根据房屋ID获取缴费记录: houseId=2
2025-08-19T11:20:24.197+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 根据房屋ID获取所有缴费记录: houseId=2
2025-08-19T11:20:24.475+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询到2条缴费记录
2025-08-19T11:20:24.476+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:24.476+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 查询到2条缴费记录
2025-08-19T11:20:24.477+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:27.493+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:20:27.493+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:20:27.493+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:20:27.493+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:20:27.494+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:27.494+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:27.494+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:27.494+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=2, heatingYear=2025)
2025-08-19T11:20:27.726+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=2, 户号=HT2024000124, 用热状态=1
2025-08-19T11:20:27.726+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:20:27.740+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=2, 户号=HT2024000124, 用热状态=1
2025-08-19T11:20:27.740+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:20:27.761+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:20:27.761+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=2
2025-08-19T11:20:27.776+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:20:27.776+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=2
2025-08-19T11:20:28.180+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=2, heatUnitId=1, communityName=印象小区
2025-08-19T11:20:28.180+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:20:28.180+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=2, 总金额=3621.23, 已缴金额=3621.23, 欠费金额=0.00, 状态=paid
2025-08-19T11:20:28.180+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:20:28.198+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=2, heatUnitId=1, communityName=印象小区
2025-08-19T11:20:28.198+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:20:28.198+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=2, 总金额=3621.23, 已缴金额=3621.23, 欠费金额=0.00, 状态=paid
2025-08-19T11:20:28.198+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3621.23 元
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3621.23 元
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3621.23 元
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:20:28.214+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 2
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3621.23 元
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3621.23 元
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 3621.23 元
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: paid
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=已缴清, 显示实际缴费=true, 剩余金额=0.00
2025-08-19T11:20:28.232+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 2
2025-08-19T11:20:28.249+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T11:20:28.249+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=3, 金额=2000.00, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:28.249+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=4, 金额=1621.23, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:28.249+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T11:20:28.249+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:20:28.249+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:20:28.249+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:28.268+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 2
2025-08-19T11:20:28.268+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=3, 金额=2000.00, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:28.268+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=4, 金额=1621.23, 方式=现金, 日期=2025-07-16 00:00
2025-08-19T11:20:28.268+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 2 条记录
2025-08-19T11:20:28.268+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:20:28.268+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:20:28.269+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:34.413+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-statistics?userId=2
2025-08-19T11:20:34.413+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-statistics?userId=2
2025-08-19T11:20:34.414+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 获取用户故障统计信息，用户ID: 2
2025-08-19T11:20:34.419+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/weixin/fault-history?userId=2&page=1&size=10
2025-08-19T11:20:34.419+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/weixin/fault-history?userId=2&page=1&size=10
2025-08-19T11:20:34.420+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 获取用户故障历史记录，用户ID: 2, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:20:34.659+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障统计信息，房屋ID: 2
2025-08-19T11:20:34.689+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取房屋故障历史记录，房屋ID: 2, 页码: 1, 每页大小: 10, 状态: null
2025-08-19T11:20:34.757+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 获取故障历史记录成功，共0条记录
2025-08-19T11:20:34.758+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:20:34.883+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取故障统计信息成功，房屋ID: 2, 总数: 0
2025-08-19T11:20:34.884+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:21:01.031+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/login
2025-08-19T11:21:01.031+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/login
2025-08-19T11:21:01.958+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:21:02.366+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:21:02.367+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:21:05.738+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:21:05.738+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:21:05.739+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:05.739+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:05.740+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:21:05.740+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:21:05.740+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:05.740+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:05.981+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:21:05.981+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:21:05.991+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:21:05.991+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:21:06.021+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:21:06.021+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:21:06.029+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:21:06.029+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:21:06.428+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:21:06.428+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:21:06.428+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:21:06.428+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:21:06.444+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:21:06.444+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:21:06.444+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:21:06.444+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:21:06.470+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:21:06.470+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:21:06.470+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:21:06.479+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:21:06.479+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:21:06.479+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:21:06.694+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:21:06.729+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:21:06.729+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:21:06.729+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:21:06.729+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:21:06.729+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:21:06.731+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:21:06.731+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:21:06.731+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:21:06.732+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:21:06.732+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:21:12.224+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill-detail
2025-08-19T11:21:12.224+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill-detail
2025-08-19T11:21:12.225+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取账单详情请求: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:21:12.462+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 获取账单详情: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:21:12.922+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:21:12.923+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:21:12.934+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:21:12.934+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:21:12.935+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:12.935+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:13.139+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:21:13.139+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:21:13.173+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:21:13.173+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:21:13.579+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:21:13.579+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:21:13.579+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:21:13.579+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:21:13.613+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:21:13.613+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:21:13.613+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:21:13.829+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:21:13.863+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:21:13.863+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:21:13.863+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:21:13.864+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:21:13.864+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:21:23.030+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:21:23.030+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:21:23.031+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:23.031+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:21:23.280+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:21:23.280+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:21:23.315+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:21:23.315+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:21:23.737+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:21:23.737+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:21:23.737+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:21:23.737+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:21:23.771+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:21:23.771+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:21:23.771+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:21:23.977+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:21:24.021+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:21:24.021+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:21:24.021+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:21:24.021+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:21:24.022+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:21:29.599+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T11:21:29.599+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T11:21:29.600+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=14, heatingYear=2025
2025-08-19T11:21:29.600+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=14, heatingYear=2025
2025-08-19T11:21:29.896+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=14, 状态=unpaid, 总金额=3631.96, 已缴金额=0.00
2025-08-19T11:21:29.896+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: unpaid
2025-08-19T11:21:29.896+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单未缴费，返回账单信息
2025-08-19T11:21:29.896+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:21:29.896+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:21:30.314+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:21:30.315+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:21:30.315+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:21:30.349+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:21:30.349+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:21:30.349+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:21:30.573+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:21:30.614+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:21:30.614+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:21:30.614+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T11:21:30.615+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:22:12.370+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:22:12.371+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:22:12.371+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:12.372+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:12.375+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:22:12.375+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:22:12.376+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:12.376+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:12.605+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:22:12.605+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:22:12.618+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:22:12.618+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:22:12.641+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:22:12.641+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:22:12.652+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:22:12.652+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:22:13.056+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:22:13.056+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:22:13.056+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:22:13.056+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:22:13.065+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:22:13.065+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:22:13.065+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:22:13.065+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:22:13.090+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:22:13.090+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:22:13.090+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:22:13.101+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:22:13.101+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:22:13.101+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:22:13.293+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:22:13.300+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:22:13.328+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:22:13.328+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:22:13.328+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:22:13.328+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:22:13.328+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:22:13.340+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:22:13.340+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:22:13.340+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:22:13.340+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:22:13.341+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:22:22.023+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill-detail
2025-08-19T11:22:22.023+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill-detail
2025-08-19T11:22:22.023+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 获取账单详情请求: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:22:22.287+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 获取账单详情: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:22:22.737+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:22:22.738+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:22:22.750+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:22:22.750+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:22:22.750+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:22.750+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:22.954+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:22:22.954+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:22:22.988+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:22:22.988+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:22:23.403+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:22:23.403+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:22:23.404+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:22:23.404+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:22:23.443+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:22:23.443+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:22:23.443+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:22:23.655+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:22:23.693+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:22:23.693+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:22:23.693+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:22:23.693+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:22:23.694+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:22:28.751+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:22:28.752+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:22:28.752+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:28.752+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:22:28.999+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:22:28.999+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:22:29.033+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:22:29.034+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:22:29.437+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:22:29.437+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:22:29.437+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:22:29.437+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:22:29.471+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:22:29.471+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:22:29.471+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:22:29.697+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:22:29.731+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:22:29.731+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:22:29.731+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:22:29.731+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:22:29.731+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:23:01.079+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill-detail
2025-08-19T11:23:01.079+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill-detail
2025-08-19T11:23:01.080+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.controller.WeixinController    : 获取账单详情请求: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:23:01.334+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.heating.service.impl.BillServiceImpl   : 获取账单详情: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:23:01.771+08:00  INFO 22940 --- [http-nio-8889-exec-2] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:23:01.771+08:00 DEBUG 22940 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:23:01.785+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:23:01.785+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:23:01.786+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:23:01.786+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:23:01.988+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:23:01.988+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:23:02.025+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:23:02.025+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:23:02.440+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:23:02.440+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:23:02.440+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:23:02.440+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:23:02.474+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:23:02.474+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:23:02.474+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:23:02.672+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:23:02.706+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:23:02.706+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:23:02.706+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:23:02.706+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:23:02.706+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:24:19.208+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:24:19.209+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:24:19.209+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:24:19.209+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:24:19.450+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:24:19.450+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:24:19.484+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:24:19.484+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:24:19.919+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:24:19.919+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:24:19.919+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:24:19.919+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:24:19.953+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:24:19.953+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:24:19.953+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:24:20.161+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:24:20.207+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:24:20.207+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:24:20.207+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:24:20.207+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:24:20.208+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:24:35.583+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T11:24:35.583+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T11:24:35.583+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=14, heatingYear=2025
2025-08-19T11:24:35.583+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=14, heatingYear=2025
2025-08-19T11:24:35.857+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=14, 状态=unpaid, 总金额=3631.96, 已缴金额=0.00
2025-08-19T11:24:35.857+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: unpaid
2025-08-19T11:24:35.857+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单未缴费，返回账单信息
2025-08-19T11:24:35.857+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:24:35.857+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:24:36.286+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:24:36.286+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:24:36.286+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:24:36.323+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:24:36.323+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:24:36.323+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:24:36.547+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:24:36.581+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:24:36.582+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:24:36.582+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T11:24:36.582+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:28:10.211+08:00  WARN 22940 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6b907fce (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-08-19T11:29:22.682+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T11:29:22.682+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T11:29:22.682+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=14, heatingYear=2025
2025-08-19T11:29:22.682+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=14, heatingYear=2025
2025-08-19T11:29:22.968+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=14, 状态=unpaid, 总金额=3631.96, 已缴金额=0.00
2025-08-19T11:29:22.968+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: unpaid
2025-08-19T11:29:22.968+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 账单未缴费，返回账单信息
2025-08-19T11:29:22.968+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:29:22.968+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:29:23.383+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:29:23.383+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:29:23.383+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:29:23.418+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:29:23.418+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:29:23.418+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:29:23.618+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:29:23.652+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:29:23.652+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:29:23.652+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T11:29:23.653+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:30:25.488+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/pending-payment
2025-08-19T11:30:25.488+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/pending-payment
2025-08-19T11:30:25.489+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取待缴费账单信息请求: houseId=14, heatingYear=2025
2025-08-19T11:30:25.489+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取待缴费账单信息（SimpleBillInfoResponse格式）: houseId=14, heatingYear=2025
2025-08-19T11:30:25.775+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到账单: ID=14, 状态=unpaid, 总金额=3631.96, 已缴金额=0.00
2025-08-19T11:30:25.775+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始处理账单状态（SimpleBillInfoResponse格式）: unpaid
2025-08-19T11:30:25.775+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单未缴费，返回账单信息
2025-08-19T11:30:25.775+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:30:25.775+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:30:26.191+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:30:26.191+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:30:26.191+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:30:26.235+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:30:26.235+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:30:26.235+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:30:26.460+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:30:26.494+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:30:26.494+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:30:26.494+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 待缴费账单信息获取完成: code=200, message=查询成功
2025-08-19T11:30:26.494+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:33:30.903+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:33:30.903+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:33:30.904+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:30.904+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:30.904+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:33:30.905+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:33:30.905+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:30.905+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:31.159+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:33:31.159+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:33:31.165+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:33:31.165+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:33:31.197+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:33:31.197+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:33:31.215+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:33:31.215+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:33:31.622+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:33:31.622+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:33:31.622+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:33:31.622+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:33:32.679+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:33:32.679+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:33:32.679+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:33:32.679+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:33:33.480+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:33:33.480+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:33:33.480+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:33:33.512+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:33:34.221+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:33:45.910+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:33:45.981+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:33:46.015+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:33:46.015+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:33:46.015+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:33:46.015+08:00  INFO 22940 --- [http-nio-8889-exec-8] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:33:46.016+08:00 DEBUG 22940 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:33:46.154+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:33:46.155+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:33:46.155+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:33:46.155+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:33:46.156+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:33:46.156+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:33:46.156+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:33:46.156+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:33:46.156+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:33:46.199+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:33:46.199+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:33:46.199+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:33:46.199+08:00  INFO 22940 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:33:46.200+08:00 DEBUG 22940 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:33:55.569+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:33:55.569+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:33:55.569+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:55.569+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:55.571+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:33:55.571+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:33:55.572+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:55.572+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:33:55.814+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:33:55.814+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:33:55.848+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=0
2025-08-19T11:33:55.849+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:33:55.849+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:33:55.849+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:33:55.883+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:33:55.883+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:33:56.276+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:33:56.276+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:33:56.276+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:33:56.276+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:34:03.885+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:34:03.885+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T11:34:03.885+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:34:03.885+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T11:34:05.866+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:34:05.866+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:34:07.900+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:34:07.945+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:34:07.945+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:34:07.945+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 125.24, 计费规则ID: 1
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:34:08.150+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1089.5880 元 (账单金额 3631.96 * 最低比例 0.30)
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1089.5880 元
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1089.5880 元
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:34:08.173+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:34:08.196+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:34:08.196+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:34:08.196+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:34:08.196+08:00  INFO 22940 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:34:08.196+08:00 DEBUG 22940 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:34:08.206+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:34:08.206+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:34:08.206+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:34:08.206+08:00  INFO 22940 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:34:08.206+08:00 DEBUG 22940 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:34:48.379+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:34:48.380+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:34:48.380+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:34:48.381+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:34:48.381+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:48.381+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:48.381+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:48.381+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:48.626+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=1
2025-08-19T11:34:48.626+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:34:48.634+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=1
2025-08-19T11:34:48.634+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:34:48.660+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:34:48.660+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:34:48.667+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:34:48.667+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:34:49.073+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:34:49.073+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:34:49.073+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:34:49.073+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:34:49.085+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:34:49.085+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:34:49.085+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:34:49.085+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:34:49.106+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3631.96 元
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3631.96 元
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:34:49.107+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:34:49.119+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3631.96 元
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3631.96 元
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:34:49.120+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:34:49.140+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:34:49.140+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:34:49.141+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:34:49.141+08:00  INFO 22940 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:34:49.141+08:00 DEBUG 22940 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:34:49.154+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:34:49.154+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:34:49.154+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:34:49.154+08:00  INFO 22940 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:34:49.154+08:00 DEBUG 22940 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:34:51.214+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill-detail
2025-08-19T11:34:51.214+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill-detail
2025-08-19T11:34:51.215+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.controller.WeixinController    : 获取账单详情请求: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:34:51.462+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.heating.service.impl.BillServiceImpl   : 获取账单详情: BillDetailRequest(billId=14, userId=6, houseId=null)
2025-08-19T11:34:51.939+08:00  INFO 22940 --- [http-nio-8889-exec-5] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:34:51.940+08:00 DEBUG 22940 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:34:51.951+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:34:51.952+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:34:51.952+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:51.952+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:52.154+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=1
2025-08-19T11:34:52.154+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:34:52.189+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:34:52.189+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:34:52.629+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:34:52.629+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:34:52.629+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:34:52.629+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3631.96 元
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3631.96 元
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:34:52.669+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:34:52.704+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:34:52.704+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:34:52.704+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:34:52.704+08:00  INFO 22940 --- [http-nio-8889-exec-9] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:34:52.704+08:00 DEBUG 22940 --- [http-nio-8889-exec-9] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T11:34:55.930+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T11:34:55.930+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T11:34:55.930+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:55.930+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=14, heatingYear=2025)
2025-08-19T11:34:56.174+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=14, 户号=HT2024000132, 用热状态=1
2025-08-19T11:34:56.174+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T11:34:56.219+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T11:34:56.219+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=14
2025-08-19T11:34:56.639+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=14, heatUnitId=1, communityName=印象小区
2025-08-19T11:34:56.639+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=正常供暖
2025-08-19T11:34:56.639+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=14, 总金额=3631.96, 已缴金额=0.00, 欠费金额=0.00, 状态=unpaid
2025-08-19T11:34:56.639+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 1
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 用热状态 - 用热费: 3631.96 元
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 3631.96 元
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 0.00 元
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: unpaid
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=未缴费, 显示实际缴费=false, 剩余金额=3631.96
2025-08-19T11:34:56.684+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 14
2025-08-19T11:34:56.718+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 0
2025-08-19T11:34:56.718+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 0 条记录
2025-08-19T11:34:56.718+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T11:34:56.718+08:00  INFO 22940 --- [http-nio-8889-exec-10] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T11:34:56.718+08:00 DEBUG 22940 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T18:43:49.307+08:00  INFO 22940 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T18:43:49.308+08:00  INFO 22940 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-19T18:43:49.492+08:00  INFO 22940 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
