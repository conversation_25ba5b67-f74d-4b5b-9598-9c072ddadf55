package com.heating.service.impl;

import com.heating.dto.bill.HeatingApplicationRequest;
import com.heating.entity.bill.THeatingApplication;
import com.heating.repository.HeatingApplicationRepository;
import com.heating.service.HeatingApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用热申请服务实现类
 */
@Slf4j
@Service
public class HeatingApplicationServiceImpl implements HeatingApplicationService {

    @Autowired
    private HeatingApplicationRepository heatingApplicationRepository;

    @Override
    @Transactional
    public Long submitApplication(HeatingApplicationRequest request) {
        try {
            log.info("开始处理用热申请: houseId={}", request.getHouseId());
            
            // 数据验证
            validateApplicationRequest(request);
            
            // 检查是否已有相同季度的待审核申请
            long pendingCount = heatingApplicationRepository.countPendingApplicationsByHouseAndSeason(
                    request.getHouseId(), request.getApplySeason());
            if (pendingCount > 0) {
                throw new RuntimeException("该供暖季已有待审核的申请，请勿重复提交");
            }

            // 创建用热申请对象
            THeatingApplication application = new THeatingApplication();
            application.setHouseId(request.getHouseId());
            application.setResidentName(request.getResidentName().trim());
            application.setPhone(request.getPhone().trim());
            application.setApplySeason(request.getApplySeason().trim());
            application.setCurrentStatus(THeatingApplication.ApplicationStatus.pending);
            application.setApplyReason(request.getApplyReason() != null ? request.getApplyReason().trim() : null);
            application.setApplyTime(LocalDateTime.now());
            application.setIsNotified(false);
            application.setSource(THeatingApplication.ApplicationSource.online);

            // 保存申请记录
            THeatingApplication savedApplication = heatingApplicationRepository.save(application);
            
            log.info("用热申请提交成功，申请ID: {}", savedApplication.getId());
            return savedApplication.getId();
            
        } catch (Exception e) {
            log.error("提交用热申请失败: houseId={}, error={}", request.getHouseId(), e.getMessage(), e);
            throw new RuntimeException("提交申请失败：" + e.getMessage());
        }
    }

    @Override
    public List<THeatingApplication> getApplicationList(Long houseId) {
        try {
            log.info("获取用热申请列表: houseId={}", houseId);
            
            List<THeatingApplication> applicationList;
            if (houseId != null) {
                applicationList = heatingApplicationRepository.findByHouseIdOrderByCreatedAtDesc(houseId);
            } else {
                applicationList = heatingApplicationRepository.findAllByOrderByCreatedAtDesc();
            }
            
            log.info("获取到{}条用热申请记录", applicationList.size());
            return applicationList;
            
        } catch (Exception e) {
            log.error("获取用热申请列表失败: houseId={}, error={}", houseId, e.getMessage(), e);
            throw new RuntimeException("获取申请列表失败：" + e.getMessage());
        }
    }

    @Override
    public THeatingApplication getApplicationDetail(Long id) {
        try {
            log.info("获取用热申请详情: id={}", id);
            
            THeatingApplication application = heatingApplicationRepository.findById(id).orElse(null);
            if (application == null) {
                throw new RuntimeException("申请记录不存在");
            }
            
            log.info("获取用热申请详情成功: id={}", id);
            return application;
            
        } catch (Exception e) {
            log.error("获取用热申请详情失败: id={}, error={}", id, e.getMessage(), e);
            throw new RuntimeException("获取申请详情失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void cancelApplication(Long id) {
        try {
            log.info("取消用热申请: id={}", id);
            
            THeatingApplication application = heatingApplicationRepository.findById(id).orElse(null);
            if (application == null) {
                throw new RuntimeException("申请记录不存在");
            }
            
            if (application.getCurrentStatus() != THeatingApplication.ApplicationStatus.pending) {
                throw new RuntimeException("只能取消待审核的申请");
            }
            
            application.setCurrentStatus(THeatingApplication.ApplicationStatus.cancelled);
            application.setUpdatedAt(LocalDateTime.now());
            heatingApplicationRepository.save(application);
            
            log.info("取消用热申请成功: id={}", id);
            
        } catch (Exception e) {
            log.error("取消用热申请失败: id={}, error={}", id, e.getMessage(), e);
            throw new RuntimeException("取消申请失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void approveApplication(Long id, boolean approved, String approvedBy, String rejectReason, String remark) {
        try {
            log.info("审批用热申请: id={}, approved={}", id, approved);
            
            THeatingApplication application = heatingApplicationRepository.findById(id).orElse(null);
            if (application == null) {
                throw new RuntimeException("申请记录不存在");
            }
            
            if (application.getCurrentStatus() != THeatingApplication.ApplicationStatus.pending) {
                throw new RuntimeException("只能审批待审核的申请");
            }
            
            if (approved) {
                application.setCurrentStatus(THeatingApplication.ApplicationStatus.approved);
            } else {
                application.setCurrentStatus(THeatingApplication.ApplicationStatus.rejected);
                if (rejectReason == null || rejectReason.trim().isEmpty()) {
                    throw new RuntimeException("拒绝申请时必须填写拒绝原因");
                }
                application.setRejectReason(rejectReason.trim());
            }
            
            application.setApprovedBy(approvedBy);
            application.setApproveTime(LocalDateTime.now());
            application.setRemark(remark != null ? remark.trim() : null);
            application.setUpdatedAt(LocalDateTime.now());
            
            heatingApplicationRepository.save(application);
            
            log.info("审批用热申请成功: id={}, approved={}", id, approved);
            
        } catch (Exception e) {
            log.error("审批用热申请失败: id={}, error={}", id, e.getMessage(), e);
            throw new RuntimeException("审批申请失败：" + e.getMessage());
        }
    }

    /**
     * 验证申请请求数据
     * @param request 申请请求
     */
    private void validateApplicationRequest(HeatingApplicationRequest request) {
        if (request.getHouseId() == null) {
            throw new RuntimeException("房屋ID不能为空");
        }
        
        if (request.getResidentName() == null || request.getResidentName().trim().isEmpty()) {
            throw new RuntimeException("申请人姓名不能为空");
        }
        
        if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
            throw new RuntimeException("联系电话不能为空");
        }
        
        if (request.getApplySeason() == null || request.getApplySeason().trim().isEmpty()) {
            throw new RuntimeException("申请供暖季不能为空");
        }
        
        // 验证电话号码格式（简单验证）
        String phone = request.getPhone().trim();
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            throw new RuntimeException("请输入正确的手机号码");
        }
        
        // 验证申请人姓名长度
        if (request.getResidentName().trim().length() > 50) {
            throw new RuntimeException("申请人姓名不能超过50个字符");
        }
        
        // 验证申请原因长度
        if (request.getApplyReason() != null && request.getApplyReason().trim().length() > 500) {
            throw new RuntimeException("申请原因不能超过500个字符");
        }
    }
}
