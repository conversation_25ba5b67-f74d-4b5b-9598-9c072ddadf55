<view class="container" wx:if="{{!loading && applicationDetail}}">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">申请详情</text>
  </view>

  <!-- 状态卡片 -->
  <view class="card status-card">
    <view class="status-content">
      <view class="status-icon {{applicationDetail.statusClass}}">
        <text wx:if="{{applicationDetail.currentStatus === 'pending'}}">⏳</text>
        <text wx:elif="{{applicationDetail.currentStatus === 'approved'}}">✅</text>
        <text wx:elif="{{applicationDetail.currentStatus === 'rejected'}}">❌</text>
        <text wx:elif="{{applicationDetail.currentStatus === 'cancelled'}}">🚫</text>
        <text wx:else>❓</text>
      </view>
      <view class="status-info">
        <text class="status-text {{applicationDetail.statusClass}}">{{applicationDetail.statusText}}</text>
        <text class="status-desc" wx:if="{{applicationDetail.currentStatus === 'pending'}}">
          我们将在3个工作日内完成审核
        </text>
        <text class="status-desc" wx:elif="{{applicationDetail.currentStatus === 'approved'}}">
          申请已通过，工作人员将联系您安排开通
        </text>
        <text class="status-desc" wx:elif="{{applicationDetail.currentStatus === 'rejected'}}">
          申请未通过，请查看拒绝原因
        </text>
        <text class="status-desc" wx:elif="{{applicationDetail.currentStatus === 'cancelled'}}">
          申请已取消
        </text>
      </view>
    </view>
  </view>

  <!-- 申请信息卡片 -->
  <view class="card info-card">
    <view class="card-header">
      <text class="card-title">申请信息</text>
    </view>
    <view class="info-content">
      <view class="info-row">
        <text class="label">申请供暖季</text>
        <text class="value">{{applicationDetail.applySeason}}</text>
      </view>
      <view class="info-row">
        <text class="label">申请人姓名</text>
        <text class="value">{{applicationDetail.residentName}}</text>
      </view>
      <view class="info-row">
        <text class="label">联系电话</text>
        <view class="value-with-action">
          <text class="value">{{applicationDetail.phone}}</text>
          <text class="copy-btn" bindtap="copyText" data-text="{{applicationDetail.phone}}">复制</text>
        </view>
      </view>
      <view class="info-row" wx:if="{{applicationDetail.applyReason}}">
        <text class="label">申请原因</text>
        <text class="value reason">{{applicationDetail.applyReason}}</text>
      </view>
      <view class="info-row">
        <text class="label">申请时间</text>
        <text class="value">{{applicationDetail.applyTimeFormatted}}</text>
      </view>
      <view class="info-row" wx:if="{{applicationDetail.approveTime}}">
        <text class="label">审批时间</text>
        <text class="value">{{applicationDetail.approveTimeFormatted}}</text>
      </view>
      <view class="info-row" wx:if="{{applicationDetail.approvedBy}}">
        <text class="label">审批人</text>
        <text class="value">{{applicationDetail.approvedBy}}</text>
      </view>
    </view>
  </view>

  <!-- 拒绝原因卡片 -->
  <view class="card reject-card" wx:if="{{applicationDetail.rejectReason}}">
    <view class="card-header">
      <text class="card-title reject-title">拒绝原因</text>
    </view>
    <view class="reject-content">
      <text class="reject-text">{{applicationDetail.rejectReason}}</text>
    </view>
  </view>

  <!-- 管理员备注卡片 -->
  <view class="card remark-card" wx:if="{{applicationDetail.remark}}">
    <view class="card-header">
      <text class="card-title">管理员备注</text>
    </view>
    <view class="remark-content">
      <text class="remark-text">{{applicationDetail.remark}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section" wx:if="{{applicationDetail.currentStatus === 'pending'}}">
    <button class="action-btn cancel-btn" bindtap="cancelApplication">
      取消申请
    </button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <text class="loading-text">加载中...</text>
  </view>
</view>
