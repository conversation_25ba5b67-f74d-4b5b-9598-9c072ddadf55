const { billApi } = require('../../api/index.js');

Page({
  data: {
    loading: false,
    billDetail: null,
    basicInfo: {},
    billInfo: {},
    paymentRecords: [],
    overdueInfo: {},
    heatingYear: null
  },

  onLoad(options) {
    // 获取传入的供暖年度参数
    const heatingYear = options.heatingYear ? parseInt(options.heatingYear) : null;
    this.setData({ heatingYear });
    
    // 加载账单详情
    this.loadBillDetail();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadBillDetail();
  },

  /**
   * 加载账单详情
   */
  async loadBillDetail() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showModal({
        title: '提示',
        content: '请先绑定户号',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '加载中...'
    });

    try {
      console.log('开始加载账单详情...');

      // 如果没有指定年度，使用当前供暖年度
      let heatingYear = this.data.heatingYear;
      if (!heatingYear) {
        heatingYear = this.getCurrentHeatingYear();
        this.setData({ heatingYear });
      }

      // 调用新的简化账单信息接口
      const response = await billApi.getSimpleBillInfo({
        houseId: userInfo.houseId,
        heatingYear: heatingYear
      });

      console.log('简化账单信息响应:', response);

      if (response && response.code === 200) {
        const data = response.data;

        // 构建基本信息（无论是否有账单都要显示）
        const basicInfo = data && data.houseInfo ? {
          houseNumber: data.houseInfo.houseNumber,
          address: data.houseInfo.address,
          area: data.houseInfo.area,
          heatingYear: data.houseInfo.heatingYear,
          heatingStatusText: data.houseInfo.heatingStatusText
        } : {};

        // 检查是否有账单数据
        if (data && data.billId && data.paymentStatusInfo && data.paymentStatusInfo.paymentStatus !== 'no_bill') {
          // 有账单数据时构建完整的账单信息
          const billInfo = {
            billId: data.billId,
            amount:data.billFeeInfo.amount,
            // 新的字段结构
            heatingFee: data.billFeeInfo.heatingFee,
            feeTypeName: data.billFeeInfo.feeTypeName,
            overdueAmount: data.billFeeInfo.overdueAmount,
            totalPayableAmount: data.billFeeInfo.totalPayableAmount,
            actualPaidAmount: data.billFeeInfo.actualPaidAmount,
            // 保持兼容性的字段
            totalAmount: data.billFeeInfo.heatingFee,
            paidAmount: data.billFeeInfo.actualPaidAmount,
            remainingAmount: data.paymentStatusInfo.remainingAmount,
            // 状态信息
            status: data.paymentStatusInfo.paymentStatus,
            statusText: data.paymentStatusInfo.paymentStatusText,
            showActualPaidAmount: data.paymentStatusInfo.showActualPaidAmount,
            dueDate: data.paymentStatusInfo.dueDate,
            lastPaidDate: data.paymentStatusInfo.lastPaidDate,
            // 添加单价字段
            unitPrice: data.billFeeInfo.unitPrice || '0.00'
          };

          this.setData({
            billDetail: data,
            basicInfo: basicInfo,
            billInfo: billInfo,
            paymentRecords: data.paymentRecords || [],
            overdueInfo: {}
          });

          console.log('简化账单信息加载成功');
        } else {
          // 没有账单数据时，显示基本信息和空账单状态
          this.setData({
            billDetail: data,
            basicInfo: basicInfo,
            billInfo: {
              status: 'no_bill',
              statusText: response.message || '暂无账单',
              unitPrice: '0.00'
            },
            paymentRecords: [],
            overdueInfo: {}
          });

          console.log('基本信息加载成功，暂无账单数据:', response.message);
        }
      } else {
        // 请求失败时的处理
        console.warn('获取账单信息失败:', response ? response.message : '未知错误');
        this.setData({
          billDetail: null,
          basicInfo: {},
          billInfo: { status: 'no_bill', statusText: '暂无账单' },
          paymentRecords: [],
          overdueInfo: {}
        });
      }

    } catch (error) {
      console.error('加载账单详情失败:', error);

      // 不再显示错误弹窗，而是显示空状态
      this.setData({
        billDetail: null,
        basicInfo: {},
        billInfo: { status: 'no_bill', statusText: '暂无账单' },
        paymentRecords: [],
        overdueInfo: {}
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },

  /**
   * 获取当前供暖年度
   */
  getCurrentHeatingYear() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // getMonth()返回0-11

    // 如果是1-10月，则属于上一年的供暖年度
    if (currentMonth <= 6) {
      return currentYear - 1;
    } else {
      // 如果是11-12月，则属于当年的供暖年度
      return currentYear;
    }
  },

  /**
   * 去缴费
   */
  goToPay() {
    const { billInfo } = this.data;
    if (!billInfo || !billInfo.billId) {
      wx.showToast({
        title: '账单信息异常',
        icon: 'none'
      });
      return;
    }

    // 检查是否需要缴费
    if (billInfo.status === 'paid') {
      wx.showToast({
        title: '账单已缴清',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/payment/index?billId=${billInfo.billId}`
    });
  },

  /**
   * 查看缴费记录详情
   */
  viewPaymentDetail(e) {
    const paymentId = e.currentTarget.dataset.paymentId;
    if (paymentId) {
      wx.navigateTo({
        url: `/pages/payment/detail?paymentId=${paymentId}`
      });
    }
  },

  /**
   * 切换供暖年度
   */
  changeHeatingYear() {
    const currentHeatingYear = this.getCurrentHeatingYear();
    const years = [];
    const yearValues = [];

    // 生成最近5年的供暖年度选项
    for (let i = 0; i < 5; i++) {
      const year = currentHeatingYear - i;
      years.push(`${year}-${year + 1}供暖年度`);
      yearValues.push(year);
    }

    wx.showActionSheet({
      itemList: years,
      success: (res) => {
        const selectedYear = yearValues[res.tapIndex];
        this.setData({ heatingYear: selectedYear });
        this.loadBillDetail();
      }
    });
  },

  /**
   * 刷新数据
   */
  onRefresh() {
    this.loadBillDetail();
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        }
      }
    });
  },

  /**
   * 分享账单
   */
  onShareAppMessage() {
    return {
      title: '我的供暖账单',
      path: '/pages/bind/bill-view',
      imageUrl: '/images/share-bill.png'
    };
  }
});
