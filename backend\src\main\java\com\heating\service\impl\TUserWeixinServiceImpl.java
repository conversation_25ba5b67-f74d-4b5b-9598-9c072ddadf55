package com.heating.service.impl;

import com.heating.dto.user.*;
import com.heating.entity.user.TUserWeixin;
import com.heating.entity.House;
import com.heating.repository.HeatUnitRepository;
import com.heating.repository.user.TUserWeixinRepository;
import com.heating.service.HeatUnitService;
import com.heating.service.TUserWeixinService;
import com.heating.repository.HouseRepository;
import com.heating.utils.JwtUtils;
import com.heating.vo.user.WeixinLoginResponse;
import com.heating.vo.user.WeixinUserInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class TUserWeixinServiceImpl implements TUserWeixinService {

    @Autowired
    private TUserWeixinRepository userWeixinRepository;

    @Autowired
    HeatUnitService heatUnitService;

    @Autowired
    private HouseRepository houseRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private  JwtUtils jwtUtils;

    @Override
    public WeixinLoginResponse weixinLogin(WeixinAuthLoginRequest request) {
        WeixinLoginResponse response = new WeixinLoginResponse();
        
        try {
            // 通过code获取微信用户信息
            Map<String, Object> weixinInfo = getWeixinUserInfo(request.getCode());
            String openid = (String) weixinInfo.get("openid");
            String unionid = (String) weixinInfo.get("unionid");
            
            if (openid == null) {
                response.setSuccess(false);
                response.setMessage("微信授权失败");
                return response;
            }

            // 查询用户是否已存在
            TUserWeixin user = getByOpenid(openid);
            boolean isNewUser = false;
            
            if (user == null) {
                // 新用户，创建账号
                user = new TUserWeixin();
                user.setOpenid(openid);
                user.setUnionid(unionid);
                user.setUsername("wx_" + openid.substring(0, 8)); // 生成默认用户名
                user.setName("微信用户");
                user.setStatus(1);
                user.setCreateTime(LocalDateTime.now());
                user.setUpdateTime(LocalDateTime.now());
                
                userWeixinRepository.save(user);
                isNewUser = true;
            }

            // 检查用户状态
            if (user.getStatus() != null && user.getStatus() == 0) {
                response.setSuccess(false);
                response.setMessage("账户已被禁用");
                return response;
            }

            // 生成token
            String token = jwtUtils.generateWeixinToken(user);

            // 更新最后登录时间
            updateLastLoginTime(user.getId());

            // 构建用户信息
            WeixinUserInfoResponse userInfo = getUserInfo(user.getId());

            response.setSuccess(true);
            response.setMessage("登录成功");
            response.setToken(token);
            response.setUserInfo(userInfo);
            response.setNewUser(isNewUser);

        } catch (Exception e) {
            log.error("微信登录失败", e);
            response.setSuccess(false);
            response.setMessage("微信登录失败：" + e.getMessage());
        }

        return response;
    }

    @Override
    @Transactional
    public boolean weixinRegister(WeixinPhoneRegisterRequest request) {
        try {
            // 通过code获取微信用户信息
            Map<String, Object> weixinInfo = getWeixinUserInfo(request.getCode());
            String openid = (String) weixinInfo.get("openid");
            String unionid = (String) weixinInfo.get("unionid");
            
            if (openid == null) {
                throw new RuntimeException("微信授权失败");
            }

            // 检查openid是否已存在
            if (getByOpenid(openid) != null) {
                throw new RuntimeException("该微信账号已注册");
            }

            // 检查手机号是否已存在
            if (request.getPhone() != null && !request.getPhone().isEmpty()) {
                Optional<TUserWeixin> existingUser = userWeixinRepository.findByPhone(request.getPhone());
                if (existingUser.isPresent()) {
                    throw new RuntimeException("手机号已被注册");
                }
            }

            // 创建用户
            TUserWeixin user = new TUserWeixin();
            user.setOpenid(openid);
            user.setUnionid(unionid);
            user.setUsername("wx_" + openid.substring(0, 8));
            user.setName(request.getName());
            user.setPhone(request.getPhone());
            user.setNickName(request.getNickName());
            user.setAvatar(request.getAvatar());
            user.setGender(request.getGender());
            user.setStatus(1);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());

            userWeixinRepository.save(user);
            return true;

        } catch (Exception e) {
            log.error("微信注册失败", e);
            throw new RuntimeException("注册失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean bindHouseByUsername(String username, String houseNumber) {
        try {
            // 查询用户
            TUserWeixin user = getByUsername(username);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 检查用户是否已经绑定了户号
            if (user.getHouseId() != null && user.getHouseId() > 0) {
                throw new RuntimeException("您已绑定户号，无法重复绑定");
            }

            // 查询房屋信息
            Optional<House> houseOpt = houseRepository.findByHouseNumber(houseNumber);
            if (!houseOpt.isPresent()) {
                throw new RuntimeException("户号不存在，请检查户号是否正确");
            }

            House house = houseOpt.get();

            // 检查户号是否已被其他用户绑定
            Optional<TUserWeixin> existingBind = userWeixinRepository.findByHouseId(house.getId());
            if (existingBind.isPresent()) {
                throw new RuntimeException("该户号已被其他用户绑定，一个户号只能绑定一个用户账号");
            }

            // 绑定户号
            user.setHouseId(house.getId());
            user.setHeatUnitId(house.getHeatUnitId());
            user.setUpdateTime(LocalDateTime.now());

            userWeixinRepository.save(user);
            
            log.info("用户 {} 成功绑定户号 {}", username, houseNumber);
            return true;

        } catch (Exception e) {
            log.error("绑定户号失败: 用户={}, 户号={}", username, houseNumber, e);
            throw new RuntimeException("绑定失败：" + e.getMessage());
        }
    }

    @Override
    public WeixinUserInfoResponse getUserInfo(Long userId) {
        Optional<TUserWeixin> userOpt = userWeixinRepository.findById(userId);
        if (!userOpt.isPresent()) {
            return null;
        }

        TUserWeixin user = userOpt.get();
        WeixinUserInfoResponse response = new WeixinUserInfoResponse();
        BeanUtils.copyProperties(user, response);

        // 如果绑定了户号，获取房屋信息
        if (user.getHouseId() != null && user.getHouseId() > 0) {
            Optional<House> houseOpt = houseRepository.findById(user.getHouseId());
            if (houseOpt.isPresent()) {
                House house = houseOpt.get();
                response.setHouseId(house.getId());
                // 调用服务方法获取小区名字
                String communityName = heatUnitService.getCommunityNameByHouseId(house.getId());
                response.setHeatUnitId(house.getHeatUnitId());
                response.setHouseNumber(house.getHouseNumber());
                response.setAddress(communityName+" "+house.getRoomNo());
                response.setArea(house.getBuiltArea() != null ? house.getBuiltArea().toString() : "");
                response.setHeatingStatus(house.getIsHeating());
                response.setHeatingStatusText(getHeatingStatusText(house.getIsHeating()));
            }
        }
        return response;
    }

    @Override
    public TUserWeixin getByUsername(String username) {
        Optional<TUserWeixin> user = userWeixinRepository.findByUsername(username);
        return user.orElse(null);
    }

    @Override
    public TUserWeixin getByOpenid(String openid) {
        Optional<TUserWeixin> user = userWeixinRepository.findByOpenid(openid);
        return user.orElse(null);
    }

    @Override
    public void updateLastLoginTime(Long userId) {
        Optional<TUserWeixin> userOpt = userWeixinRepository.findById(userId);
        if (userOpt.isPresent()) {
            TUserWeixin user = userOpt.get();
            user.setLastLoginTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            userWeixinRepository.save(user);
        }
    }

    private String getHeatingStatusText(Integer status) {
        if (status == null) {
            return "供暖";
        }
        switch (status) {
            case 0:
                return "不供暖";
            case 1:
                return "供暖";
            default:
                return "供暖";
        }
    }

    @Override
    public Map<String, Object> getWeixinUserInfo(String code) {
        try {
             //DO: 实际项目中需要需要调用微信API调用微信API
             // 通过code获取acaess_token和opepidid
            // String url = "https://api.weixin.qq.com/sns/jscodee2session";
            // Map<String, String> params = new HashMap<>();
            // params.put("appid",eee
            
            // 模拟返回数据（实际开发中需要真实调用微信API）
            Map<String, Object> result = new HashMap<>();
            result.put("openid", "mock_openid_" + System.currentTimeMillis());
            result.put("unionid", "mock_" + System.currentTimeMillis());
            return result;
            
        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            throw new RuntimeException("获取微信用户信息失败");
        }
    }

    @Override
    public WeixinLoginResponse login(WeixinLoginRequest request) {
        WeixinLoginResponse response = new WeixinLoginResponse();
        
        try {
            // 通过手机号查询用户
            Optional<TUserWeixin> userOpt = userWeixinRepository.findByPhone(request.getPhone());
            if (!userOpt.isPresent()) {
                response.setSuccess(false);
                response.setMessage("手机号或密码错误");
                return response;
            }

            TUserWeixin user = userOpt.get();

            // 检查用户状态
            if (user.getStatus() != null && user.getStatus() == 0) {
                response.setSuccess(false);
                response.setMessage("账户已被禁用");
                return response;
            }

            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                response.setSuccess(false);
                response.setMessage("手机号或密码错误");
                return response;
            }
            String token = jwtUtils.generateWeixinToken(user);


            // 更新最后登录时间
            updateLastLoginTime(user.getId());

            // 构建用户信息
            WeixinUserInfoResponse userInfo = getUserInfo(user.getId());

            response.setSuccess(true);
            response.setMessage("登录成功");
            response.setToken(token);
            response.setUserInfo(userInfo);
            response.setNewUser(false);

        } catch (Exception e) {
            log.error("用户登录失败", e);
            response.setSuccess(false);
            response.setMessage("登录失败：" + e.getMessage());
        }

        return response;
    }

    @Override
    @Transactional
    public boolean register(WeixinRegisterRequest request) {
        try {
            log.info("开始注册用户: {}", request.getUsername());
            
            // 检查用户名是否已存在
            Optional<TUserWeixin> existingUser = userWeixinRepository.findByUsername(request.getUsername());
            if (existingUser.isPresent()) {
                throw new RuntimeException("用户名已存在");
            }

            // 检查手机号是否已存在
            if (request.getPhone() != null && !request.getPhone().isEmpty()) {
                Optional<TUserWeixin> existingPhone = userWeixinRepository.findByPhone(request.getPhone());
                if (existingPhone.isPresent()) {
                    throw new RuntimeException("手机号已被注册");
                }
            }

            // 创建用户
            TUserWeixin user = new TUserWeixin();
            user.setUsername(request.getUsername());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setName(request.getName());
            user.setPhone(request.getPhone());
            user.setNickName(request.getNickName());
            user.setAvatar(request.getAvatar());
            user.setGender(request.getGender());
            user.setStatus(1);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());

            userWeixinRepository.save(user);
            log.info("用户注册成功: {}", user.getUsername());
            return true;

        } catch (Exception e) {
            log.error("用户注册失败", e);
            throw new RuntimeException("注册失败：" + e.getMessage());
        }
    }

    @Override
    public TUserWeixin getById(Long userId) {
        Optional<TUserWeixin> userOpt = userWeixinRepository.findById(userId);
        return userOpt.orElse(null);
    }
}

