package com.heating.service;

import com.heating.dto.bill.HeatingApplicationRequest;
import com.heating.entity.bill.THeatingApplication;
import java.util.List;

/**
 * 用热申请服务接口
 */
public interface HeatingApplicationService {
    
    /**
     * 提交用热申请
     * @param request 申请请求数据
     * @return 申请ID
     */
    Long submitApplication(HeatingApplicationRequest request);
    
    /**
     * 获取申请列表
     * @param houseId 房屋ID，为空时查询所有
     * @return 申请列表
     */
    List<THeatingApplication> getApplicationList(Long houseId);
    
    /**
     * 获取申请详情
     * @param id 申请ID
     * @return 申请详情
     */
    THeatingApplication getApplicationDetail(Long id);
    
    /**
     * 取消申请
     * @param id 申请ID
     */
    void cancelApplication(Long id);
    
    /**
     * 审批申请
     * @param id 申请ID
     * @param approved 是否通过
     * @param approvedBy 审批人
     * @param rejectReason 拒绝原因（拒绝时必填）
     * @param remark 管理员备注
     */
    void approveApplication(Long id, boolean approved, String approvedBy, String rejectReason, String remark);
}
