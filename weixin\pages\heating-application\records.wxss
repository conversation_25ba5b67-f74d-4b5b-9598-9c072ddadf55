/* 页面容器 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 统计卡片 */
.stats-card {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-divider {
  width: 1rpx;
  background-color: #e0e0e0;
  margin: 0 30rpx;
}

/* 记录列表 */
.record-list {
  height: calc(100vh - 200rpx);
}

.record-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-season {
  flex: 1;
}

.season-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.record-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.record-status.approved {
  background-color: #d4edda;
  color: #155724;
}

.record-status.rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.record-status.cancelled {
  background-color: #e2e3e5;
  color: #383d41;
}

.status-text {
  font-weight: 500;
}

/* 记录内容 */
.record-content {
  margin-bottom: 20rpx;
}

.content-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.content-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.value.reason {
  line-height: 1.5;
}

.value.time {
  color: #666;
}

.value.reject-reason {
  color: #ff4757;
  line-height: 1.5;
}

/* 操作按钮 */
.record-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: none;
}

.cancel-btn {
  background-color: #ff4757;
  color: #fff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-btn {
  background-color: #007aff;
  color: #fff;
  border-radius: 44rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
