package com.heating.controller;
import com.heating.service.HeatUnitService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.heating.dto.ApiResponse;
import com.heating.dto.heatUnit.HeatUnitDTO;
import com.heating.dto.heatUnit.HeatUnitCountResponse;
import java.util.List;

@RestController
@RequestMapping("/api/heatunits")
public class HeatUnitController {
    private static final Logger logger = LoggerFactory.getLogger(HeatUnitController.class);

    @Autowired
    private HeatUnitService heatUnitService;

    @GetMapping("/list")
    public ResponseEntity<ApiResponse<List<HeatUnitDTO>>> getHeatUnits() {
        logger.info("Accessing GET /api/heatunits/list");
        try {
            List<HeatUnitDTO> units = heatUnitService.getHeatUnits();
            logger.info("Retrieved {} heat units", units.size());
            return ResponseEntity.ok(ApiResponse.success("获取供热单元列表成功", units));
        } catch (Exception e) {
            logger.error("Error in GET /api/heatunits/list: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error(e.getMessage())); 
        }
    } 

    @GetMapping("/count")
    public ResponseEntity<ApiResponse<HeatUnitCountResponse>> getHeatUnitCount() {
        HeatUnitCountResponse count = heatUnitService.getHeatUnitCount();
        return ResponseEntity.ok(ApiResponse.success("获取供热单元总数成功", count));
    }

    /**
     * 根据住户id获取小区名字
     * @param houseId 住户id
     * @return 小区名字
     */
    @GetMapping("/community-name/{houseId}")
    public ResponseEntity<ApiResponse<String>> getCommunityNameByHouseId(@PathVariable Long houseId) {
        logger.info("Accessing GET /api/heatunits/community-name/{}", houseId);
        try {
            String communityName = heatUnitService.getCommunityNameByHouseId(houseId);
            if (communityName != null) {
                logger.info("Successfully retrieved community name for houseId {}: {}", houseId, communityName);
                return ResponseEntity.ok(ApiResponse.success("获取小区名字成功", communityName));
            } else {
                logger.warn("No community name found for houseId: {}", houseId);
                return ResponseEntity.ok(ApiResponse.error("未找到对应的小区信息"));
            }
        } catch (Exception e) {
            logger.error("Error in GET /api/heatunits/community-name/{}: {}", houseId, e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取小区名字失败: " + e.getMessage()));
        }
    }
}