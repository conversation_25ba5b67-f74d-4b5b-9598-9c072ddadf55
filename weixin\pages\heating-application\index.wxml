<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">申请用热服务</text>
    <view class="header-actions">
      <view class="record-btn" bindtap="goToRecords">
        <text class="iconfont">📋</text>
        <text>申请记录</text>
      </view>
    </view>
  </view>

  <!-- 房屋信息卡片 -->
  <view class="card house-card">
    <view class="card-header">
      <text class="card-title">房屋信息</text>
    </view>
    <view class="house-info">
      <view class="info-row">
        <text class="label">房屋地址</text>
        <text class="value">{{houseInfo.address}}</text>
      </view>
      <view class="info-row">
        <text class="label">户号</text>
        <text class="value">{{houseInfo.houseNumber}}</text>
      </view>
    </view>
  </view>

  <!-- 申请信息卡片 -->
  <view class="card form-card">
    <view class="card-header">
      <text class="card-title">申请信息</text>
    </view>
    
    <!-- 申请人姓名 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>申请人姓名</text>
      </view>
      <input class="form-input" 
             placeholder="请输入申请人姓名"
             value="{{formData.residentName}}"
             bindinput="onResidentNameInput"
             maxlength="50">
      </input>
    </view>

    <!-- 联系电话 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>联系电话</text>
      </view>
      <input class="form-input" 
             placeholder="请输入联系电话"
             value="{{formData.phone}}"
             bindinput="onPhoneInput"
             type="number"
             maxlength="11">
      </input>
    </view>

    <!-- 申请供暖季 -->
    <view class="form-item">
      <view class="form-label">
        <text class="required">*</text>
        <text>申请供暖季</text>
      </view>
      <picker mode="selector" 
              range="{{seasonOptions}}"
              value="{{seasonOptions.indexOf(formData.applySeason)}}"
              bindchange="onSeasonChange">
        <view class="picker-input {{formData.applySeason ? 'selected' : 'placeholder'}}">
          <text>{{formData.applySeason || '请选择供暖季'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 申请原因 -->
    <view class="form-item">
      <view class="form-label">
        <text>申请原因</text>
        <text class="optional">（可选）</text>
      </view>
      <textarea class="textarea-input" 
                placeholder="请输入申请原因，如：新入住、恢复供暖、房屋装修完成等"
                value="{{formData.applyReason}}"
                bindinput="onReasonInput"
                maxlength="500">
      </textarea>
      <view class="char-count">{{formData.applyReason.length}}/500</view>
    </view>
  </view>

  <!-- 温馨提示 -->
  <view class="card tips-card">
    <view class="card-header">
      <text class="card-title">温馨提示</text>
    </view>
    <view class="tips-content">
      <view class="tip-item">
        <text class="tip-icon">•</text>
        <text class="tip-text">申请提交后，我们将在3个工作日内完成审核</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">•</text>
        <text class="tip-text">审核通过后，将安排工作人员上门开通用热服务</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">•</text>
        <text class="tip-text">如有疑问，请联系客服热线：400-123-4567</text>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" 
            bindtap="submitApplication"
            disabled="{{!canSubmit}}">
      提交申请
    </button>
  </view>
</view>
