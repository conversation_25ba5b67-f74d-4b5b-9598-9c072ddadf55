const { heatingApplicationApi } = require('../../api/index.js');

Page({
  data: {
    houseInfo: {
      id: null,
      address: '',
      houseNumber: ''
    },
    formData: {
      residentName: '',
      phone: '',
      applySeason: '',
      applyReason: ''
    },
    seasonOptions: [
      '2024-2025',
      '2025-2026',
      '2026-2027',
      '2027-2028'
    ],
    canSubmit: false
  },

  onLoad(options) {
    this.loadUserInfo();
    this.initSeasonOptions();
  },

  /**
   * 初始化供暖季选项
   */
  initSeasonOptions() {
    const currentYear = new Date().getFullYear();
    const seasonOptions = [];
    
    // 生成当前年份及后续几年的供暖季选项
    for (let i = 0; i < 5; i++) {
      const startYear = currentYear + i;
      const endYear = startYear + 1;
      seasonOptions.push(`${startYear}-${endYear}`);
    }
    
    this.setData({
      seasonOptions: seasonOptions,
      'formData.applySeason': seasonOptions[0] // 默认选择第一个
    });
  },

  /**
   * 加载用户信息
   */
  async loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定房屋信息',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    this.setData({
      houseInfo: {
        id: userInfo.houseId,
        address: userInfo.address || '',
        houseNumber: userInfo.houseNumber || ''
      },
      'formData.residentName': userInfo.realName || '',
      'formData.phone': userInfo.phone || ''
    });

    this.checkCanSubmit();
  },

  /**
   * 申请人姓名输入
   */
  onResidentNameInput(e) {
    this.setData({
      'formData.residentName': e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 联系电话输入
   */
  onPhoneInput(e) {
    this.setData({
      'formData.phone': e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 供暖季选择
   */
  onSeasonChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.applySeason': this.data.seasonOptions[index]
    });
    this.checkCanSubmit();
  },

  /**
   * 申请原因输入
   */
  onReasonInput(e) {
    this.setData({
      'formData.applyReason': e.detail.value
    });
    this.checkCanSubmit();
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { residentName, phone, applySeason } = this.data.formData;
    const canSubmit = residentName.trim() && phone.trim() && applySeason.trim();
    this.setData({ canSubmit });
  },

  /**
   * 提交申请
   */
  async submitApplication() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善申请信息',
        icon: 'none'
      });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.data.formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    const requestData = {
      houseId: this.data.houseInfo.id,
      residentName: this.data.formData.residentName.trim(),
      phone: this.data.formData.phone.trim(),
      applySeason: this.data.formData.applySeason.trim(),
      applyReason: this.data.formData.applyReason.trim()
    };

    console.log('提交用热申请:', requestData);

    heatingApplicationApi.submitApplication(requestData).then(res => {
      wx.hideLoading();
      console.log('用热申请响应:', res);

      if (res.code === 200) {
        wx.showToast({
          title: '申请提交成功',
          icon: 'success',
          duration: 2000
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      } else {
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      console.error('用热申请失败:', error);
      wx.showToast({
        title: error.message || '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 跳转到申请记录页面
   */
  goToRecords() {
    wx.navigateTo({
      url: '/pages/heating-application/records'
    });
  }
});
