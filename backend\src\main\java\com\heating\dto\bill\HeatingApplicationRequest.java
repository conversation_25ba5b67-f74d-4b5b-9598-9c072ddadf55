package com.heating.dto.bill;

import lombok.Data;
import lombok.ToString;

/**
 * 用热申请请求DTO
 * 用于接收前端提交的申请数据
 */
@Data
@ToString
public class HeatingApplicationRequest {
    
    /**
     * 房屋ID
     */
    private Long houseId;
    
    /**
     * 申请人姓名
     */
    private String residentName;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 申请供暖季，如 2025-2026
     */
    private String applySeason;
    
    /**
     * 申请原因（可选填）
     */
    private String applyReason;
}
