package com.heating.controller;

import com.heating.dto.payment.PaymentSubmitRequest;
import com.heating.entity.House;
import com.heating.entity.bill.TBill;
import com.heating.entity.bill.TPayment;
import com.heating.repository.HouseRepository;
import com.heating.repository.TBillRepository;
import com.heating.repository.TPaymentRepository;
import com.heating.service.HeatUnitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 缴费测试控制器
 * 用于调试缴费功能的问题
 */
@Slf4j
@RestController
@RequestMapping("/api/test/payment")
public class PaymentTestController {

    @Autowired
    private TPaymentRepository paymentRepository;

    @Autowired
    private TBillRepository billRepository;

    @Autowired
    private HouseRepository houseRepository;

    @Autowired
    private HeatUnitService heatUnitService;

    /**
     * 测试数据库连接
     */
    @GetMapping("/test-db")
    public ResponseEntity<Map<String, Object>> testDatabase() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试查询账单
            long billCount = billRepository.count();
            result.put("billCount", billCount);
            log.info("账单总数: {}", billCount);

            // 测试查询房屋
            long houseCount = houseRepository.count();
            result.put("houseCount", houseCount);
            log.info("房屋总数: {}", houseCount);

            // 测试查询缴费记录
            long paymentCount = paymentRepository.count();
            result.put("paymentCount", paymentCount);
            log.info("缴费记录总数: {}", paymentCount);

            result.put("status", "success");
            result.put("message", "数据库连接正常");

        } catch (Exception e) {
            log.error("数据库测试失败: {}", e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "数据库连接失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 测试创建最简单的缴费记录
     */
    @PostMapping("/test-create")
    public ResponseEntity<Map<String, Object>> testCreatePayment(@RequestBody PaymentSubmitRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试创建缴费记录: {}", request);

            // 1. 检查账单是否存在
            Optional<TBill> billOpt = billRepository.findById(request.getBillId());
            if (!billOpt.isPresent()) {
                result.put("status", "error");
                result.put("message", "账单不存在: " + request.getBillId());
                return ResponseEntity.ok(result);
            }
            TBill bill = billOpt.get();
            log.info("找到账单: {}", bill.getId());

            // 2. 检查房屋是否存在
            Optional<House> houseOpt = houseRepository.findById(request.getHouseId());
            if (!houseOpt.isPresent()) {
                result.put("status", "error");
                result.put("message", "房屋不存在: " + request.getHouseId());
                return ResponseEntity.ok(result);
            }
            House house = houseOpt.get();
            log.info("找到房屋: {}", house.getId());

            // 3. 创建最简单的缴费记录
            TPayment payment = new TPayment();
            payment.setBillId(request.getBillId());
            payment.setHouseId(request.getHouseId());
            payment.setAmount(request.getAmount() != null ? request.getAmount() : BigDecimal.valueOf(100.00));
            payment.setPaymentMethod(TPayment.PaymentMethod.wechat);
            payment.setTransactionNo("TEST" + System.currentTimeMillis());
            payment.setPaymentDate(LocalDateTime.now());
            payment.setRemark("测试缴费记录");
            
            // 设置必填字段
            payment.setRoomNo(house.getRoomNo() != null ? house.getRoomNo() : "");
            payment.setHeatYear(bill.getHeatYear() != null ? bill.getHeatYear() : 2024);

            log.info("准备保存缴费记录: billId={}, houseId={}, amount={}, roomNo={}, heatYear={}", 
                    payment.getBillId(), payment.getHouseId(), payment.getAmount(), 
                    payment.getRoomNo(), payment.getHeatYear());

            // 4. 保存到数据库
            TPayment savedPayment = paymentRepository.save(payment);
            log.info("缴费记录保存成功: ID={}", savedPayment.getId());

            result.put("status", "success");
            result.put("message", "缴费记录创建成功");
            result.put("paymentId", savedPayment.getId());
            result.put("billId", savedPayment.getBillId());
            result.put("houseId", savedPayment.getHouseId());
            result.put("amount", savedPayment.getAmount());

        } catch (Exception e) {
            log.error("测试创建缴费记录失败: {}", e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "创建失败: " + e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 获取测试数据
     */
    @GetMapping("/test-data")
    public ResponseEntity<Map<String, Object>> getTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取第一个账单
            Optional<TBill> firstBill = billRepository.findAll().stream().findFirst();
            if (firstBill.isPresent()) {
                TBill bill = firstBill.get();
                result.put("testBillId", bill.getId());
                result.put("testHouseId", bill.getHouseId());
                result.put("billInfo", Map.of(
                    "id", bill.getId(),
                    "houseId", bill.getHouseId(),
                    "heatYear", bill.getHeatYear(),
                    "totalAmount", bill.getTotalAmount()
                ));
            }

            // 获取第一个房屋
            Optional<House> firstHouse = houseRepository.findAll().stream().findFirst();
            if (firstHouse.isPresent()) {
                House house = firstHouse.get();
                result.put("houseInfo", Map.of(
                    "id", house.getId(),
                    "roomNo", house.getRoomNo() != null ? house.getRoomNo() : "",
                    "houseNumber", house.getHouseNumber() != null ? house.getHouseNumber() : ""
                ));
            }

            result.put("status", "success");

        } catch (Exception e) {
            log.error("获取测试数据失败: {}", e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "获取测试数据失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 测试根据住户id获取小区名字的接口
     */
    @GetMapping("/test-community-name")
    public ResponseEntity<Map<String, Object>> testCommunityName() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取第一个房屋进行测试
            Optional<House> firstHouse = houseRepository.findAll().stream().findFirst();
            if (firstHouse.isPresent()) {
                House house = firstHouse.get();
                Long houseId = house.getId();

                log.info("测试住户id: {}", houseId);

                // 调用服务方法获取小区名字
                String communityName = heatUnitService.getCommunityNameByHouseId(houseId);

                result.put("houseId", houseId);
                result.put("houseInfo", Map.of(
                    "id", house.getId(),
                    "roomNo", house.getRoomNo() != null ? house.getRoomNo() : "",
                    "houseNumber", house.getHouseNumber() != null ? house.getHouseNumber() : "",
                    "heatUnitId", house.getHeatUnitId() != null ? house.getHeatUnitId() : "null"
                ));
                result.put("communityName", communityName);
                result.put("status", "success");
                result.put("message", communityName != null ? "获取小区名字成功" : "未找到小区信息");

                log.info("测试结果: houseId={}, communityName={}", houseId, communityName);

            } else {
                result.put("status", "error");
                result.put("message", "数据库中没有房屋数据");
            }

        } catch (Exception e) {
            log.error("测试获取小区名字失败: {}", e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "测试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}
