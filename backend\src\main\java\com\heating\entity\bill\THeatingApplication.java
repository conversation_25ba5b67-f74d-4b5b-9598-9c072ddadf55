package com.heating.entity.bill;

import jakarta.persistence.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用热申请实体类
 * 对应数据表：t_heating_application
 */
@Data
@Entity
@Table(name = "t_heating_application")
public class THeatingApplication {
    
    /**
     * 主键，自增ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 外键，关联 t_house 表
     */
    @Column(name = "house_id", nullable = false)
    private Long houseId;

    /**
     * 申请人姓名
     */
    @Column(name = "resident_name", nullable = false, length = 50)
    private String residentName;

    /**
     * 联系电话
     */
    @Column(name = "phone", nullable = false, length = 20)
    private String phone;

    /**
     * 申请供暖季，如 2025-2026
     */
    @Column(name = "apply_season", nullable = false, length = 20)
    private String applySeason;

    /**
     * 申请状态：pending=待审核, approved=已通过, rejected=已拒绝, cancelled=已取消
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "current_status", nullable = false)
    private ApplicationStatus currentStatus = ApplicationStatus.pending;

    /**
     * 申请原因（可选填）
     */
    @Column(name = "apply_reason", columnDefinition = "TEXT")
    private String applyReason;

    /**
     * 申请提交时间
     */
    @Column(name = "apply_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 审批完成时间
     */
    @Column(name = "approve_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 审批人（管理员账号或姓名）
     */
    @Column(name = "approved_by", length = 50)
    private String approvedBy;

    /**
     * 拒绝原因
     */
    @Column(name = "reject_reason", columnDefinition = "TEXT")
    private String rejectReason;

    /**
     * 管理员备注
     */
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

      /**
     * 需要支付金额
     */
    @Column(name = "paid_amount")
    private BigDecimal paidAmount;

    /**
     * 是否已发送通知：0=未通知，1=已通知
     */
    @Column(name = "is_notified", nullable = false)
    private Boolean isNotified = false;

    /**
     * 申请来源：online=小程序在线, offline=线下窗口
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "source", nullable = false)
    private ApplicationSource source = ApplicationSource.online;

    /**
     * 记录创建时间
     */
    @Column(name = "created_at", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "updated_at", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 申请状态枚举
     */
    public enum ApplicationStatus {
        pending,    // 待审核
        approved,   // 已通过
        rejected,   // 已拒绝
        cancelled   // 已取消
    }

    /**
     * 申请来源枚举
     */
    public enum ApplicationSource {
        online,     // 小程序在线
        offline     // 线下窗口
    }

    /**
     * 创建时自动设置时间
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        createdAt = now;
        updatedAt = now;
        if (applyTime == null) {
            applyTime = now;
        }
    }

    /**
     * 更新时自动设置时间
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
