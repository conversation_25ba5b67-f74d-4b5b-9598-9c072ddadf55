const { faultApi } = require('../../../api/index.js');

Page({
  data: {
    // 故障基本信息
    faultDetail: {},

    // 跟踪信息
    trackingInfo: {},

    // 附件信息
    attachments: [],

    // 加载状态
    loading: true,

    // 用户信息
    userInfo: null,

    // 状态样式
    statusClass: '',

    // 故障ID
    faultId: null
  },

  onLoad(options) {
    console.log('故障详情页面参数:', options);
    const faultId = options.faultId || options.id;

    if (!faultId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 保存faultId以便重试时使用
    this.setData({
      faultId: faultId
    });

    this.loadUserInfo();
    this.loadFaultDetail(faultId);
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      userInfo: userInfo
    });
  },

  // 加载故障详情和跟踪信息
  async loadFaultDetail(faultId) {
    try {
      this.setData({
        loading: true
      });

      console.log('开始加载故障详情，故障ID:', faultId);

      const result = await faultApi.getFaultDetailWithTracking(faultId);

      console.log('故障详情API响应:', result);

      if (result.code === 200) {
        const { faultDetail, trackingInfo } = result.data;

        // 处理状态样式类名
        let statusClass = '';
        switch (faultDetail.fault_status) {
          case '待确认':
            statusClass = 'pending';
            break;
          case '已确认':
            statusClass = 'confirmed';
            break;
          case '已退回':
            statusClass = 'returned';
            break;
          default:
            statusClass = 'unknown';
        }

        this.setData({
          faultDetail: faultDetail,
          trackingInfo: trackingInfo,
          attachments: faultDetail.attachments || [],
          statusClass: statusClass,
          loading: false
        });

        console.log('故障详情加载成功');
      } else {
        console.error('故障详情API返回错误:', result);
        throw new Error(result.message || '加载故障详情失败');
      }
    } catch (error) {
      console.error('加载故障详情失败:', error);
      this.setData({
        loading: false
      });

      let errorMessage = '加载失败，请重试';
      if (error.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      wx.showModal({
        title: '加载失败',
        content: errorMessage,
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 重试加载
            this.loadFaultDetail(this.data.faultId);
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        }
      });
    }
  },

  // 预览图片
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const urls = this.data.attachments
      .filter(item => item.file_type === '图片')
      .map(item => item.file_path);

    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});