/* 页面容器 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 状态卡片 */
.status-card {
  padding: 40rpx 30rpx;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.status-icon.pending {
  background-color: #fff3cd;
}

.status-icon.approved {
  background-color: #d4edda;
}

.status-icon.rejected {
  background-color: #f8d7da;
}

.status-icon.cancelled {
  background-color: #e2e3e5;
}

.status-info {
  flex: 1;
}

.status-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.status-text.pending {
  color: #856404;
}

.status-text.approved {
  color: #155724;
}

.status-text.rejected {
  color: #721c24;
}

.status-text.cancelled {
  color: #383d41;
}

.status-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 信息卡片 */
.info-content {
  padding: 20rpx 30rpx 30rpx;
}

.info-row {
  display: flex;
  margin-bottom: 24rpx;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.value.reason {
  line-height: 1.6;
}

.value-with-action {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 20rpx;
}

.copy-btn {
  font-size: 24rpx;
  color: #007aff;
  padding: 8rpx 16rpx;
  background-color: #f0f8ff;
  border-radius: 12rpx;
  flex-shrink: 0;
}

/* 拒绝原因卡片 */
.reject-card {
  border-left: 4rpx solid #ff4757;
}

.reject-title {
  color: #ff4757;
}

.reject-content {
  padding: 20rpx 30rpx 30rpx;
}

.reject-text {
  font-size: 28rpx;
  color: #ff4757;
  line-height: 1.6;
}

/* 管理员备注卡片 */
.remark-card {
  border-left: 4rpx solid #007aff;
}

.remark-content {
  padding: 20rpx 30rpx 30rpx;
}

.remark-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 操作按钮 */
.action-section {
  padding: 40rpx 0;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.cancel-btn {
  background-color: #ff4757;
  color: #fff;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
