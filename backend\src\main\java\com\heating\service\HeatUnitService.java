package com.heating.service;

import java.util.List;

import com.heating.dto.heatUnit.HeatUnitDTO;
import com.heating.dto.heatUnit.HeatUnitCountResponse;

public interface HeatUnitService {
    /**
     * 获取热用户列表
     * @return 热用户列表
     */
    List<HeatUnitDTO> getHeatUnits();

    /**
     * 获取热用户总数
     * @return 热用户总数
     */
    HeatUnitCountResponse getHeatUnitCount();

    /**
     * 根据住户id获取小区名字
     * @param houseId 住户id
     * @return 小区名字，如果未找到则返回null
     */
    String getCommunityNameByHouseId(Long houseId);
}