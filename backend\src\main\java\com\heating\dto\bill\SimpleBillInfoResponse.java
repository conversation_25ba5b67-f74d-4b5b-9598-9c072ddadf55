package com.heating.dto.bill;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 简化的账单信息响应DTO
 * 根据用热状态返回不同的费用信息
 */
@Data
public class SimpleBillInfoResponse {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 账单数据
     */
    private BillData data;
    
    /**
     * 账单数据内部类
     */
    @Data
    public static class BillData {
        
        /**
         * 账单ID
         */
        private Long billId;
        
        /**
         * 房屋基本信息
         */
        private HouseInfo houseInfo;
        
        /**
         * 账单费用信息
         */
        private BillFeeInfo billFeeInfo;
        
        /**
         * 缴费状态信息
         */
        private PaymentStatusInfo paymentStatusInfo;

        /**
         * 缴费记录列表
         */
        private List<PaymentRecord> paymentRecords;
    }
    
    /**
     * 房屋信息
     */
    @Data
    public static class HouseInfo {
        
        /**
         * 房屋ID
         */
        private Long houseId;
        
        /**
         * 户号
         */
        private String houseNumber;
        
        /**
         * 房屋地址
         */
        private String address;
        
        /**
         * 房屋面积
         */
        private Double area;
        
        /**
         * 用热状态：1-用热，0-不用热
         */
        private Integer isHeating;
        
        /**
         * 用热状态文本
         */
        private String heatingStatusText;
        
        /**
         * 供暖年度
         */
        private Integer heatingYear;
    }
    
    /**
     * 账单费用信息
     */
    @Data
    public static class BillFeeInfo {

        /**
         * 用热费用/管网维护费
         * - 用热状态：显示为"用热费"
         * - 不用热状态：显示为"管网维护费"
         */
        private BigDecimal heatingFee;

        /**
         * 费用名称
         * - 用热状态：返回"用热费"
         * - 不用热状态：返回"管网维护费"
         */
        private String feeTypeName;

        /**
         * 单价（元/平方米）
         */
        private BigDecimal unitPrice;
        /**
         * 账单金额
         *
         */
        private BigDecimal amount;
        /**
         * 欠费金额
         * 直接从账单表的overdue_amount字段获取
         */
        private BigDecimal overdueAmount;

        /**
         * 应缴费金额
         * 用热费/管网维护费 + 欠费金额
         */
        private BigDecimal totalPayableAmount;

        /**
         * 实际缴费金额（仅已缴费状态显示）
         * 从账单表的paid_amount字段获取
         */
        private BigDecimal actualPaidAmount;
    }
    
    /**
     * 缴费状态信息
     */
    @Data
    public static class PaymentStatusInfo {
        
        /**
         * 缴费状态：unpaid-未缴费，partial_paid-部分缴费，paid-已缴费
         */
        private String paymentStatus;
        
        /**
         * 缴费状态文本
         */
        private String paymentStatusText;
        
        /**
         * 是否显示实际缴费金额
         * true-已缴费状态显示，false-未缴费状态不显示
         */
        private Boolean showActualPaidAmount;
        
        /**
         * 剩余未缴金额
         */
        private BigDecimal remainingAmount;
        
        /**
         * 缴费截止日期
         */
        private String dueDate;
        
        /**
         * 最后缴费日期
         */
        private String lastPaidDate;
    }

    /**
     * 缴费记录信息
     */
    @Data
    public static class PaymentRecord {

        /**
         * 缴费记录ID
         */
        private Long paymentId;

        /**
         * 缴费金额
         */
        private BigDecimal amount;

        /**
         * 缴费方式
         */
        private String paymentMethod;

        /**
         * 缴费方式文本
         */
        private String paymentMethodText;

        /**
         * 缴费日期
         */
        private String paymentDate;

        /**
         * 交易流水号
         */
        private String transactionNo;

        /**
         * 备注
         */
        private String remark;
    }
}
