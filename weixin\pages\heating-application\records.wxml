<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <text class="page-title">用热申请记录</text>
  </view>

  <!-- 统计信息 -->
  <view class="stats-card">
    <view class="stat-item">
      <text class="stat-number">{{pendingCount}}</text>
      <text class="stat-label">待审核</text>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <text class="stat-number">{{approvedCount}}</text>
      <text class="stat-label">已通过</text>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <text class="stat-number">{{recordList.length}}</text>
      <text class="stat-label">总申请</text>
    </view>
  </view>

  <!-- 申请记录列表 -->
  <scroll-view class="record-list" scroll-y="true" enable-back-to-top="true">
    <view class="record-item" wx:for="{{recordList}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
      <view class="record-header">
        <view class="record-season">
          <text class="season-text">{{item.applySeason}} 供暖季</text>
        </view>
        <view class="record-status {{item.statusClass}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>
      
      <view class="record-content">
        <view class="content-row">
          <text class="label">申请人：</text>
          <text class="value">{{item.residentName}}</text>
        </view>
        <view class="content-row">
          <text class="label">联系电话：</text>
          <text class="value">{{item.phone}}</text>
        </view>
        <view class="content-row" wx:if="{{item.applyReason}}">
          <text class="label">申请原因：</text>
          <text class="value reason">{{item.applyReason}}</text>
        </view>
        <view class="content-row">
          <text class="label">申请时间：</text>
          <text class="value time">{{item.applyTimeFormatted}}</text>
        </view>
        <view class="content-row" wx:if="{{item.approveTime}}">
          <text class="label">审批时间：</text>
          <text class="value time">{{item.approveTimeFormatted}}</text>
        </view>
        <view class="content-row" wx:if="{{item.rejectReason}}">
          <text class="label">拒绝原因：</text>
          <text class="value reject-reason">{{item.rejectReason}}</text>
        </view>
      </view>

      <view class="record-actions" wx:if="{{item.currentStatus === 'pending'}}">
        <button class="action-btn cancel-btn" 
                bindtap="cancelApplication" 
                data-id="{{item.id}}"
                catchtap="true">
          取消申请
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{recordList.length === 0 && !loading}}">
      <view class="empty-icon">🏠</view>
      <text class="empty-title">暂无申请记录</text>
      <text class="empty-desc">您还没有提交过用热申请</text>
      <button class="empty-btn" bindtap="goToApply">立即申请</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view>
