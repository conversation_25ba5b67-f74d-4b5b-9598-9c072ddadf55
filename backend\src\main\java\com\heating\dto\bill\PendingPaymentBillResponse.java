package com.heating.dto.bill;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 待缴费账单信息响应DTO
 * 根据不同的缴费状态和供暖状态返回相应的待缴费信息
 */
@Data
public class PendingPaymentBillResponse {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 待缴费账单数据
     */
    private PendingBillData data;
    
    /**
     * 待缴费账单数据内部类
     */
    @Data
    public static class PendingBillData {
        
        /**
         * 账单ID
         */
        private Long billId;
        
        /**
         * 房屋基本信息
         */
        private HouseInfo houseInfo;
        
        /**
         * 缴费选项列表
         */
        private List<PaymentOption> paymentOptions;
        
        /**
         * 特殊情况说明（如用热申请补交费用）
         */
        private String specialNote;
        
        /**
         * 是否有待缴费账单
         */
        private Boolean hasPendingBill;
    }
    
    /**
     * 房屋基本信息
     */
    @Data
    public static class HouseInfo {
        
        /**
         * 房屋ID
         */
        private Long houseId;
        
        /**
         * 房屋编号
         */
        private String houseNumber;
        
        /**
         * 住户姓名
         */
        private String residentName;
        
        /**
         * 房屋面积
         */
        private BigDecimal area;
        
        /**
         * 供暖年度
         */
        private Integer heatingYear;
        
        /**
         * 当前供暖状态
         */
        private Integer isHeating;
        
        /**
         * 供暖状态文本
         */
        private String heatingStatusText;
        
        /**
         * 小区名称
         */
        private String communityName;
    }
    
    /**
     * 缴费选项
     */
    @Data
    public static class PaymentOption {
        
        /**
         * 缴费类型：heating=用热缴费, maintenance=管网维护费, supplement=补交费用
         */
        private String feeType;
        
        /**
         * 缴费类型名称
         */
        private String feeTypeName;
        
        /**
         * 缴费金额
         */
        private BigDecimal amount;
        
        /**
         * 单价（元/平方米）
         */
        private BigDecimal unitPrice;
        
        /**
         * 欠费金额
         */
        private BigDecimal overdueAmount;
        
        /**
         * 应缴费总金额（缴费金额 + 欠费金额）
         */
        private BigDecimal totalPayableAmount;
        
        /**
         * 备注信息
         */
        private String remark;
        
        /**
         * 是否推荐选项
         */
        private Boolean isRecommended;
    }
    
    /**
     * 创建成功响应
     */
    public static PendingPaymentBillResponse success(String message, PendingBillData data) {
        PendingPaymentBillResponse response = new PendingPaymentBillResponse();
        response.setCode(200);
        response.setMessage(message);
        response.setData(data);
        return response;
    }
    
    /**
     * 创建无待缴费账单响应
     */
    public static PendingPaymentBillResponse noPendingBill(String message) {
        PendingPaymentBillResponse response = new PendingPaymentBillResponse();
        response.setCode(200);
        response.setMessage(message);
        
        PendingBillData data = new PendingBillData();
        data.setHasPendingBill(false);
        response.setData(data);
        
        return response;
    }
    
    /**
     * 创建错误响应
     */
    public static PendingPaymentBillResponse error(String message) {
        PendingPaymentBillResponse response = new PendingPaymentBillResponse();
        response.setCode(400);
        response.setMessage(message);
        return response;
    }
}
