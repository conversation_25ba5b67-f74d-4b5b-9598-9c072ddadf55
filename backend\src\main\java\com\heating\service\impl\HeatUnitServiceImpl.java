package com.heating.service.impl;

import com.heating.repository.HeatUnitRepository;
import com.heating.repository.HouseRepository;
import com.heating.service.HeatUnitService;
import com.heating.entity.House;
import com.heating.entity.THeatUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.heating.dto.heatUnit.HeatUnitDTO;
import com.heating.dto.heatUnit.HeatUnitCountResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class HeatUnitServiceImpl implements HeatUnitService {

    @Autowired
    private HeatUnitRepository heatUnitRepository;

    @Autowired
    private HouseRepository houseRepository;

    @Override
    public List<HeatUnitDTO> getHeatUnits() {
        return heatUnitRepository.findHeatUnits();
    }

    @Override
    public HeatUnitCountResponse getHeatUnitCount() {
        // 统计热用户总数
        long count = heatUnitRepository.count();
        return new HeatUnitCountResponse((int) count);
    }

    @Override
    public String getCommunityNameByHouseId(Long houseId) {
        try {
            log.info("根据住户id获取小区名字: houseId={}", houseId);

            if (houseId == null) {
                log.warn("住户id为空");
                return null;
            }

            // 1. 根据住户id查询房屋信息
            Optional<House> houseOpt = houseRepository.findById(houseId);
            if (!houseOpt.isPresent()) {
                log.warn("未找到住户信息: houseId={}", houseId);
                return null;
            }

            House house = houseOpt.get();
            Long heatUnitId = house.getHeatUnitId();

            if (heatUnitId == null) {
                log.warn("住户未关联热用户: houseId={}", houseId);
                return null;
            }

            // 2. 根据热用户id查询热用户信息
            Optional<THeatUnit> heatUnitOpt = heatUnitRepository.findById(heatUnitId);
            if (!heatUnitOpt.isPresent()) {
                log.warn("未找到热用户信息: heatUnitId={}", heatUnitId);
                return null;
            }

            THeatUnit heatUnit = heatUnitOpt.get();
            String communityName = heatUnit.getName();

            log.info("成功获取小区名字: houseId={}, heatUnitId={}, communityName={}",
                    houseId, heatUnitId, communityName);

            return communityName;

        } catch (Exception e) {
            log.error("根据住户id获取小区名字失败: houseId={}, error={}", houseId, e.getMessage(), e);
            return null;
        }
    }
}